import path from "path";
import react from "@vitejs/plugin-react";
import { defineConfig, loadEnv } from "vite";

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
	// Bestimme welche .env Datei geladen werden soll
	const envFile =
		process.env.NODE_ENV === "production" ? ".env.production" : ".env.local";
	const env = loadEnv(mode, process.cwd(), "");

	return {
		plugins: [react()],
		resolve: {
			alias: {
				"@": path.resolve(__dirname, "./src"),
			},
		},
		define: {
			// Explizit die Environment Variables aus der gewählten Datei laden
			__ENV_FILE__: JSON.stringify(envFile),
		},
		envPrefix: ["VITE_"], // Only load env vars that start with VITE_
		server: {
			host: "0.0.0.0",
			allowedHosts: [
				"localhost",
				"dash.innov8-it.de",
				"99eaf722-7a2e-40f9-a464-8b9eba121c94-00-25mjidvwes4o4.picard.replit.dev",
				"macbook"
			],
		},
		preview: {
			port: 5173,
			host: "0.0.0.0",
		},
		build: {
			outDir: "dist",
			sourcemap: false,
			minify: "esbuild",
			rollupOptions: {
				output: {
					manualChunks: {
						vendor: ["react", "react-dom"],
						ui: [
							"@radix-ui/react-dialog",
							"@radix-ui/react-select",
							"@radix-ui/react-checkbox",
							"@radix-ui/react-tabs",
							"@radix-ui/react-scroll-area",
							"@radix-ui/react-progress",
							"@radix-ui/react-slot",
						],
						router: ["react-router-dom"],
						forms: ["react-hook-form", "@hookform/resolvers"],
						icons: ["lucide-react"],
						pdf: ["@react-pdf/renderer"],
						convex: ["convex"],
						clerk: ["@clerk/clerk-react"],
						utils: ["date-fns", "clsx", "tailwind-merge"],
					},
				},
			},
			target: "esnext",
			reportCompressedSize: false,
			chunkSizeWarningLimit: 1000, // Increase warning limit
		},
	};
});
