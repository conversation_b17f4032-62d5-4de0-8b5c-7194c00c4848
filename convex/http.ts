import { httpRouter } from "convex/server";
import { internal } from "./_generated/api";
import { httpAction } from "./_generated/server";

const http = httpRouter();

http.route({
	path: "/resend-webhook",
	method: "POST",
	handler: httpAction(async (ctx, req) => {
		const event = await req.json();
		await ctx.runMutation(internal.system.email.handleEmailEvent, {
			event,
		});
		return new Response(null, { status: 200 });
	}),
});

export default http;
