import { v } from "convex/values";
import { Doc, Id } from "../_generated/dataModel";
import { mutation, query } from "../_generated/server";

/**
 * Angebot mit Kundenname abrufen
 */
export const get = query({
	args: { id: v.id("kunden_angebote") },
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const angebot = await ctx.db.get(args.id);
		if (!angebot) {
			return null;
		}
		const kunde = await ctx.db.get(angebot.kundenId);

		// Prüfen, ob es Korrekturen für dieses Angebot gibt
		const hatKorrektur = angebot.istKorrektur
			? false
			: (await ctx.db
						.query("kunden_angebote")
						.withIndex("by_original", (q) => q.eq("originalId", angebot._id))
						.first())
				? true
				: false;

		return {
			...angebot,
			kundeName: kunde?.name ?? "Unbekannter Kunde",
			erstelltAmFormatiert: new Date(angebot.erstelltAm).toLocaleDateString(
				"de-DE",
			),
			hatKorrektur,
		};
	},
});

/**
 * Angebot anhand der Nummer abrufen
 */
export const getByNummer = query({
	args: { nummer: v.string() },
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const angebot = await ctx.db
			.query("kunden_angebote")
			.withIndex("by_nummer", (q) => q.eq("nummer", args.nummer))
			.first();

		if (!angebot) {
			throw new Error("Angebot nicht gefunden.");
		}

		const kunde = await ctx.db.get(angebot.kundenId);

		const hatKorrektur = angebot.istKorrektur
			? false
			: (await ctx.db
						.query("kunden_angebote")
						.withIndex("by_original", (q) => q.eq("originalId", angebot._id))
						.first())
				? true
				: false;

		return {
			...angebot,
			kundeName: kunde?.name ?? "Unbekannter Kunde",
			erstelltAmFormatiert: new Date(angebot.erstelltAm).toLocaleDateString(
				"de-DE",
			),
			hatKorrektur,
		};
	},
});

/**
 * Alle Angebote mit Kundennamen abrufen (gruppiert wie Lieferscheine)
 */
export const list = query({
	args: {},
	handler: async (ctx) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const angebote = await ctx.db.query("kunden_angebote").collect();

		// Sammle alle Angebote und ihre Korrekturen
		const angebotMap = new Map();
		const korrekturMap = new Map();

		// Alle Angebote in Maps organisieren
		for (const angebot of angebote) {
			if (angebot.istKorrektur && angebot.originalId) {
				// Korrekturen nach Original gruppieren
				if (!korrekturMap.has(angebot.originalId.toString())) {
					korrekturMap.set(angebot.originalId.toString(), []);
				}
				korrekturMap.get(angebot.originalId.toString()).push(angebot);
			} else {
				// Originale in eigene Map
				angebotMap.set(angebot._id.toString(), angebot);
			}
		}

		const results = [];

		// Verarbeite alle Angebote
		for (const [id, angebot] of angebotMap.entries()) {
			const kunde = (await ctx.db.get(
				angebot.kundenId,
			)) as Doc<"kunden"> | null;
			const korrekturen = korrekturMap.get(id) || [];
			const hatKorrektur = korrekturen.length > 0;

			const angebotWithKunde = {
				...angebot,
				kundeName: kunde?.name || "Unbekannt",
				erstelltAmFormatiert: new Date(angebot.erstelltAm).toLocaleDateString(
					"de-DE",
				),
				hatKorrektur,
			};

			const korrekturenWithKunde = korrekturen.map(
				(korrektur: Doc<"kunden_angebote">) => ({
					...korrektur,
					kundeName: kunde?.name || "Unbekannt",
					erstelltAmFormatiert: new Date(
						korrektur.erstelltAm,
					).toLocaleDateString("de-DE"),
					hatKorrektur: false,
				}),
			);

			// Nach Version sortieren (absteigend)
			korrekturenWithKunde.sort(
				(a: any, b: any) =>
					(b.korrekturVersion || 0) - (a.korrekturVersion || 0),
			);

			if (hatKorrektur) {
				// Neueste Korrektur als Hauptangebot
				const neuesteKorrektur = korrekturenWithKunde[0];
				const andereKorrekturen = korrekturenWithKunde.slice(1);

				results.push({
					hauptAngebot: neuesteKorrektur,
					korrekturen: andereKorrekturen,
					original: angebotWithKunde,
				});
			} else {
				// Original ist Hauptangebot
				results.push({
					hauptAngebot: angebotWithKunde,
					korrekturen: [],
					original: null,
				});
			}
		}

		// Nach Erstellungsdatum sortieren (neueste zuerst)
		return results.sort(
			(a: any, b: any) => b.hauptAngebot.erstelltAm - a.hauptAngebot.erstelltAm,
		);
	},
});

/**
 * Neues Angebot im Entwurfsstatus erstellen
 */
export const create = mutation({
	args: {
		kundenId: v.id("kunden"),
		gueltigBis: v.number(),
		positionen: v.array(
			v.object({
				id: v.string(),
				titel: v.string(),
				beschreibung: v.optional(v.string()),
				menge: v.number(),
				einheit: v.string(),
				einzelpreis: v.number(),
			}),
		),
		bemerkung: v.optional(v.string()),
		gesamtsummeNetto: v.number(),
		gesamtsummeBrutto: v.number(),
	},
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const kunde = await ctx.db.get(args.kundenId);
		if (!kunde) {
			throw new Error("Kunde nicht gefunden.");
		}

		const angebotId = await ctx.db.insert("kunden_angebote", {
			...args,
			umsatzsteuer: 19,
			status: "entwurf",
			istKorrektur: false,
			erstelltAm: Date.now(),
			wurdeKorrigiert: false,
		});
		return angebotId;
	},
});

/**
 * Angebot finalisieren und Nummer vergeben
 */
export const finalize = mutation({
	args: {
		angebotId: v.id("kunden_angebote"),
	},
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const angebot = await ctx.db.get(args.angebotId);
		if (!angebot) {
			throw new Error("Angebot nicht gefunden.");
		}

		if (angebot.status === "fertig" || angebot.nummer) {
			throw new Error("Angebot wurde bereits finalisiert.");
		}

		if (angebot.istKorrektur) {
			// Korrektur finalisieren
			if (!angebot.originalId) {
				throw new Error("Korrektur hat keine Original-ID.");
			}

			const originalAngebot = await ctx.db.get(angebot.originalId);
			if (!originalAngebot || !originalAngebot.nummer) {
				throw new Error(
					"Original-Angebot nicht gefunden oder nicht finalisiert.",
				);
			}

			// Korrekturversion prüfen und anpassen
			let korrekturVersion = angebot.korrekturVersion || 1;

			// Wenn es die erste Korrektur ist, starten wir mit Version 2
			if (korrekturVersion === 1) {
				korrekturVersion = 2;
			}

			// Korrektur-Nummer erstellen: Original-Nummer + .Version
			const korrekturNummer = `${originalAngebot.nummer}.${korrekturVersion}`;

			await ctx.db.patch(args.angebotId, {
				nummer: korrekturNummer,
				korrekturVersion: korrekturVersion,
				status: "fertig",
			});

			return args.angebotId;
		} else {
			// Original-Angebot finalisieren
			const currentYear = new Date().getFullYear();
			const yearSuffix = currentYear.toString().slice(-2);

			const angebote = await ctx.db
				.query("kunden_angebote")
				.filter((q) =>
					q.and(
						q.eq(q.field("istKorrektur"), false),
						q.neq(q.field("nummer"), undefined),
						q.gte(q.field("nummer"), `AG${yearSuffix}00000`),
						q.lt(
							q.field("nummer"),
							`AG${(currentYear + 1).toString().slice(-2)}00000`,
						),
					),
				)
				.collect();

			let highestNumber = 0;
			for (const ag of angebote) {
				if (ag.nummer) {
					const numberPart = parseInt(ag.nummer.substring(4), 10); // AG + YY + XXXXX
					if (numberPart > highestNumber) {
						highestNumber = numberPart;
					}
				}
			}

			const nextNumber = highestNumber + 1;
			const nummer = `AG${yearSuffix}${nextNumber.toString().padStart(5, "0")}`;

			await ctx.db.patch(args.angebotId, {
				nummer,
				status: "fertig",
			});

			return args.angebotId;
		}
	},
});

/**
 * Korrektur erstellen
 */
export const createCorrection = mutation({
	args: {
		originalId: v.id("kunden_angebote"),
	},
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const originalAngebot = await ctx.db.get(args.originalId);
		if (!originalAngebot) {
			throw new Error("Original-Angebot nicht gefunden.");
		}

		if (originalAngebot.status !== "fertig") {
			throw new Error("Nur finalisierte Angebote können korrigiert werden.");
		}

		// Bestimmen des Basis-Originals und der Basis-Nummer
		let baseOriginalId = args.originalId;
		let baseOriginalNumber = originalAngebot.nummer;

		if (originalAngebot.istKorrektur && originalAngebot.originalId) {
			// Wenn es eine Korrektur ist, verwenden wir das ursprüngliche Original als Basis
			baseOriginalId = originalAngebot.originalId;

			// Basis-Original-Angebot laden
			const baseOriginal = await ctx.db.get(baseOriginalId);
			if (!baseOriginal) {
				throw new Error("Basis-Original-Angebot nicht gefunden.");
			}

			baseOriginalNumber = baseOriginal.nummer;
		}

		// Höchste Korrekturversion für dieses Angebot finden
		const existingCorrections = await ctx.db
			.query("kunden_angebote")
			.withIndex("by_original", (q) => q.eq("originalId", baseOriginalId))
			.collect();

		// Höchste Version finden
		let highestVersion = 0;
		for (const korrektur of existingCorrections) {
			if (
				korrektur.korrekturVersion &&
				korrektur.korrekturVersion > highestVersion
			) {
				highestVersion = korrektur.korrekturVersion;
			}
		}

		// Nächste Korrekturversion
		const nextVersion = highestVersion + 1;

		// Korrektur erstellen
		const { _id, _creationTime, nummer, ...originalData } = originalAngebot;
		const korrekturId = await ctx.db.insert("kunden_angebote", {
			...originalData,
			umsatzsteuer: 19,
			originalId: baseOriginalId,
			istKorrektur: true,
			korrekturVersion: nextVersion,
			status: "entwurf",
			nummer: undefined,
			erstelltAm: Date.now(),
		});

		return korrekturId;
	},
});

/**
 * Angebot löschen
 */
export const remove = mutation({
	args: {
		id: v.id("kunden_angebote"),
	},
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const angebot = await ctx.db.get(args.id);
		if (!angebot) {
			throw new Error("Angebot nicht gefunden.");
		}

		// Nur Entwürfe können gelöscht werden
		if (angebot.status === "fertig") {
			throw new Error("Finalisierte Angebote können nicht gelöscht werden.");
		}

		await ctx.db.delete(args.id);
		return args.id;
	},
});

/**
 * Angebot aktualisieren
 */
export const update = mutation({
	args: {
		id: v.id("kunden_angebote"),
		kundenId: v.optional(v.id("kunden")),
		positionen: v.optional(
			v.array(
				v.object({
					id: v.string(),
					titel: v.string(),
					beschreibung: v.optional(v.string()),
					menge: v.number(),
					einheit: v.string(),
					einzelpreis: v.number(),
				}),
			),
		),
		bemerkung: v.optional(v.string()),
		gesamtsummeNetto: v.optional(v.number()),
		gesamtsummeBrutto: v.optional(v.number()),
		gueltigBis: v.optional(v.number()),
	},
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const { id, ...updateData } = args;

		// If positions are updated, recalculate totals if not provided
		if (
			updateData.positionen &&
			(!updateData.gesamtsummeNetto || !updateData.gesamtsummeBrutto)
		) {
			// Calculate subtotal from positions
			const subtotal = updateData.positionen.reduce((acc, pos) => {
				return acc + pos.menge * pos.einzelpreis;
			}, 0);

			const taxAmount = subtotal * 0.19;
			const total = subtotal + taxAmount;

			// Update totals if not provided
			if (!updateData.gesamtsummeNetto) {
				updateData.gesamtsummeNetto = subtotal;
			}
			if (!updateData.gesamtsummeBrutto) {
				updateData.gesamtsummeBrutto = total;
			}
		}

		const cleanUpdateData = Object.fromEntries(
			Object.entries(updateData).filter(([_, value]) => value !== undefined),
		);

		await ctx.db.patch(id, cleanUpdateData);
		return id;
	},
});

/**
 * Angebot abrufen (ohne Kunden-Informationen)
 */
export const getAngebotOnly = query({
	args: { id: v.id("kunden_angebote") },
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		return await ctx.db.get(args.id);
	},
});

/**
 * Angebot-Status aktualisieren
 */
export const updateStatus = mutation({
	args: {
		id: v.id("kunden_angebote"),
		status: v.string(), // "entwurf", "fertig", "abgeschickt", "abgelehnt", "angenommen", "bezahlt"
	},
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const angebot = await ctx.db.get(args.id);
		if (!angebot) {
			throw new Error("Angebot nicht gefunden.");
		}

		// Validate status values
		const validStatuses = [
			"entwurf",
			"fertig",
			"abgeschickt",
			"abgelehnt",
			"angenommen",
			"bezahlt",
		];
		if (!validStatuses.includes(args.status)) {
			throw new Error("Ungültiger Status.");
		}

		// Only allow status changes for finalized quotes (except from entwurf to fertig)
		if (angebot.status === "entwurf" && args.status !== "fertig") {
			throw new Error("Entwürfe können nur finalisiert werden.");
		}

		// Don't allow changing back to entwurf once finalized
		if (angebot.status !== "entwurf" && args.status === "entwurf") {
			throw new Error(
				"Finalisierte Angebote können nicht zurück in den Entwurfsstatus gesetzt werden.",
			);
		}

		await ctx.db.patch(args.id, { status: args.status });
		return args.id;
	},
});

/**
 * Angebote nach Kunde abrufen
 */
export const getByKunde = query({
	args: {
		kundenId: v.id("kunden"),
	},
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const angebote = await ctx.db
			.query("kunden_angebote")
			.filter((q) => q.eq(q.field("kundenId"), args.kundenId))
			.collect();

		const kunde = await ctx.db.get(args.kundenId);

		return angebote.map((angebot) => ({
			...angebot,
			kundeName: kunde?.name ?? "Unbekannter Kunde",
			erstelltAmFormatiert: new Date(angebot.erstelltAm).toLocaleDateString(
				"de-DE",
			),
		}));
	},
});
