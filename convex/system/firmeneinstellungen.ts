import { v } from "convex/values";
import { Doc } from "../_generated/dataModel";
import { mutation, query } from "../_generated/server";

/**
 * Company settings schema for validation
 */
const firmeneinstellungenSchema = v.object({
	// Basic company information
	firmenname: v.string(),
	rechtsform: v.optional(v.string()),
	
	// Address information
	strasse: v.string(),
	plz: v.string(),
	ort: v.string(),
	land: v.optional(v.string()),
	
	// Contact information
	telefon: v.optional(v.string()),
	fax: v.optional(v.string()),
	email: v.optional(v.string()),
	website: v.optional(v.string()),
	
	// Legal and tax information
	steuernummer: v.optional(v.string()),
	umsatzsteuerID: v.optional(v.string()),
	handelsregisternummer: v.optional(v.string()),
	amtsgericht: v.optional(v.string()),
	geschaeftsfuehrer: v.optional(v.string()),
	
	// Banking information
	bankname: v.optional(v.string()),
	iban: v.optional(v.string()),
	bic: v.optional(v.string()),
	
	// Payment and delivery terms
	zahlungsbedingungen: v.optional(v.string()),
	lieferbedingungen: v.optional(v.string()),
	zahlungszielTage: v.optional(v.number()),
	
	// Additional settings
	standardUmsatzsteuer: v.optional(v.number()),
	waehrung: v.optional(v.string()),
});

/**
 * Get company settings
 * Returns the first (and should be only) company settings record
 */
export const get = query({
	args: {},
	handler: async (ctx) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Get the first company settings record
		const settings = await ctx.db.query("firmeneinstellungen").first();
		return settings;
	},
});

/**
 * Create or update company settings
 * If settings exist, update them; otherwise create new ones
 */
export const createOrUpdate = mutation({
	args: firmeneinstellungenSchema,
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Validate required fields
		if (!args.firmenname.trim()) {
			throw new Error("Firmenname ist erforderlich.");
		}
		if (!args.strasse.trim()) {
			throw new Error("Straße ist erforderlich.");
		}
		if (!args.plz.trim()) {
			throw new Error("PLZ ist erforderlich.");
		}
		if (!args.ort.trim()) {
			throw new Error("Ort ist erforderlich.");
		}

		// Validate email format if provided
		if (args.email && args.email.trim()) {
			const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
			if (!emailRegex.test(args.email.trim())) {
				throw new Error("Ungültige E-Mail-Adresse.");
			}
		}

		// Validate tax rate if provided
		if (args.standardUmsatzsteuer !== undefined && args.standardUmsatzsteuer !== null) {
			if (args.standardUmsatzsteuer < 0 || args.standardUmsatzsteuer > 100) {
				throw new Error("Umsatzsteuer muss zwischen 0 und 100 Prozent liegen.");
			}
		}

		// Validate payment terms days if provided
		if (args.zahlungszielTage !== undefined && args.zahlungszielTage !== null) {
			if (args.zahlungszielTage < 0 || args.zahlungszielTage > 365) {
				throw new Error("Zahlungsziel muss zwischen 0 und 365 Tagen liegen.");
			}
		}

		// Check if settings already exist
		const existingSettings = await ctx.db.query("firmeneinstellungen").first();

		// Prepare data with defaults
		const settingsData = {
			...args,
			// Set defaults for optional fields
			land: args.land || "Deutschland",
			standardUmsatzsteuer: args.standardUmsatzsteuer ?? 19,
			waehrung: args.waehrung || "EUR",
			zahlungszielTage: args.zahlungszielTage ?? 14,
			zahlungsbedingungen: args.zahlungsbedingungen || "Zahlbar innerhalb von 14 Tagen nach Rechnungsdatum ohne Abzug.",
			lieferbedingungen: args.lieferbedingungen || "Lieferung erfolgt nach den Allgemeinen Geschäftsbedingungen.",
		};

		if (existingSettings) {
			// Update existing settings
			await ctx.db.patch(existingSettings._id, settingsData);
			return existingSettings._id;
		} else {
			// Create new settings
			const settingsId = await ctx.db.insert("firmeneinstellungen", settingsData);
			return settingsId;
		}
	},
});

/**
 * Get default company settings for new installations
 */
export const getDefaults = query({
	args: {},
	handler: async (ctx) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		return {
			firmenname: "innov8-IT",
			rechtsform: "GmbH",
			strasse: "",
			plz: "",
			ort: "",
			land: "Deutschland",
			telefon: "",
			fax: "",
			email: "",
			website: "",
			steuernummer: "",
			umsatzsteuerID: "",
			handelsregisternummer: "",
			amtsgericht: "",
			geschaeftsfuehrer: "",
			bankname: "",
			iban: "",
			bic: "",
			zahlungsbedingungen: "Zahlbar innerhalb von 14 Tagen nach Rechnungsdatum ohne Abzug.",
			lieferbedingungen: "Lieferung erfolgt nach den Allgemeinen Geschäftsbedingungen.",
			zahlungszielTage: 14,
			standardUmsatzsteuer: 19,
			waehrung: "EUR",
		};
	},
});

/**
 * Check if company settings exist
 */
export const exists = query({
	args: {},
	handler: async (ctx) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		const settings = await ctx.db.query("firmeneinstellungen").first();
		return !!settings;
	},
});
