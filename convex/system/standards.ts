import { v } from "convex/values";
import { query } from "../_generated/server";
import { defaultAngebotConfig } from "../erstellung/angeboteConfig";
import { defaultLieferscheinConfig } from "../erstellung/lieferscheineConfig";
import { defaultUebersichtConfig } from "../erstellung/uebersichtenConfig";

/**
 * Get settings by type from the standardsConfig file
 */
export const getByType = query({
	args: {
		typ: v.string(),
	},
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Return the settings from the new config files based on type
		if (args.typ === "uebersicht") {
			return defaultUebersichtConfig.settings;
		} else if (args.typ === "lieferschein") {
			return defaultLieferscheinConfig.settings;
		} else if (args.typ === "angebot") {
			return defaultAngebotConfig.settings;
		} else {
			throw new Error(`Unbekannter Einstellungstyp: ${args.typ}`);
		}
	},
});

/**
 * Get specific settings by type
 */
export const getSettings = query({
	args: {
		type: v.string(),
	},
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Return the specific settings type from new config files
		if (args.type === "email") {
			// For email, we need to combine both email settings
			return {
				lieferschein: defaultLieferscheinConfig.emailSettings,
				uebersicht: defaultUebersichtConfig.emailSettings,
			};
		} else if (args.type === "uebersicht") {
			return defaultUebersichtConfig.settings;
		} else if (args.type === "lieferschein") {
			return defaultLieferscheinConfig.settings;
		} else if (args.type === "angebot") {
			return defaultAngebotConfig.settings;
		} else {
			throw new Error(`Unbekannter Einstellungstyp: ${args.type}`);
		}
	},
});

/**
 * Get all settings from the standardsConfig file
 */
export const getAll = query({
	args: {},
	handler: async (ctx) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("Nicht authentifiziert.");
		}

		// Return all settings from the new config files
		return {
			uebersicht: defaultUebersichtConfig.settings,
			lieferschein: defaultLieferscheinConfig.settings,
			angebot: defaultAngebotConfig.settings,
			email: {
				lieferschein: defaultLieferscheinConfig.emailSettings,
				uebersicht: defaultUebersichtConfig.emailSettings,
			},
		};
	},
});
