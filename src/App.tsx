import { SignInPage } from "@/components/_auth/SignInPage";
import { LieferscheinDetailPage } from "@/components/erstellung/lieferscheine/LieferscheinDetail";
import { MainLayout } from "@/components/layout/MainLayout";
import { AngebotePage } from "@/pages/erstellung/angebote/index";
import { LeistungenPage } from "@/pages/erstellung/leistung/index";
import { LieferscheinePage } from "@/pages/erstellung/lieferscheine/index";
import { UebersichtPage } from "@/pages/erstellung/uebersicht/index";

import AngeboteDetailPage from "@/pages/erstellung/angebote/[id]";
import { KundenDokuDetailPage } from "@/pages/kunden/doku/[id]";
import { KundenDokuPage } from "@/pages/kunden/doku/index";
import { KundenTermineDetailPage } from "@/pages/kunden/termine/[id]";
import { KundenTerminePage } from "@/pages/kunden/termine/index";
import { DashboardPage } from "@/pages/startseite/index";
import { DokuKategorienPage } from "@/pages/system/doku-kategorien/index";
import { FeedbackPage } from "@/pages/system/feedback/index";
import { FirmeneinstellungenPage } from "@/pages/system/firmeneinstellungen/index";
import { StandardsPage } from "@/pages/system/standards/index";
import { KontingentePage } from "@/pages/verwaltung/kontingente/index";
import KundenStammdatenDetailPage from "@/pages/verwaltung/kunden/[id]";
import { KundenPage } from "@/pages/verwaltung/kunden/index";
import KundenStammdatenNeuPage from "@/pages/verwaltung/kunden/neu";
import MitarbeiterDetailPage from "@/pages/verwaltung/mitarbeiter/[id]";
import { MitarbeiterPage } from "@/pages/verwaltung/mitarbeiter/index";
import MitarbeiterNeuPage from "@/pages/verwaltung/mitarbeiter/neu";
import {
	RedirectToSignIn,
	SignedIn,
	SignedOut,
	useAuth,
} from "@clerk/clerk-react";
import { useEffect } from "react";
import {
	BrowserRouter,
	Navigate,
	Outlet,
	Route,
	Routes,
} from "react-router-dom";
import { Toaster } from "sonner";

/**
 * Component to handle the layout and nested routes for authenticated users.
 */
function ProtectedAppLayout() {
	// MainLayout already contains an <Outlet /> for its children.
	// This component ensures MainLayout is rendered only when signed in.
	return (
		<MainLayout>
			<Outlet /> {/* This Outlet will render the nested protected routes */}
		</MainLayout>
	);
}

export default function App() {
	const { isLoaded, isSignedIn } = useAuth();

	// Authentication state is handled by Clerk internally

	if (!isLoaded) {
		return (
			<div className="h-screen flex items-center justify-center bg-gray-900">
				<div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500" />
			</div>
		);
	}

	return (
		<BrowserRouter>
			<Toaster
				position="bottom-right"
				toastOptions={{
					duration: 4000,
					style: {
						"--toast-duration": "4000ms",
					} as React.CSSProperties,
				}}
			/>
			<Routes>
				{/* Public route for signing in */}
				<Route path="/signin" element={<SignInPage />} />

				{/* Routes that require authentication */}
				<Route
					path="/*" // This will match any path other than /signin if not caught by more specific routes
					element={isSignedIn ? <ProtectedAppLayout /> : <RedirectToSignIn />}
				>
					{/* Default route for "/" when signed in */}
					<Route index element={<DashboardPage />} />

					{/* Kunden Routes */}
					<Route
						path="kunden"
						element={<Navigate to="/kunden/doku" replace />}
					/>
					<Route path="kunden/doku" element={<KundenDokuPage />} />
					<Route path="kunden/doku/:id" element={<KundenDokuDetailPage />} />
					<Route path="kunden/termine" element={<KundenTerminePage />} />
					<Route
						path="kunden/termine/:id"
						element={<KundenTermineDetailPage />}
					/>

					{/* Erstellung Routes */}
					<Route
						path="erstellung"
						element={<Navigate to="/erstellung/leistung" replace />}
					/>
					<Route path="erstellung/leistung" element={<LeistungenPage />} />
					<Route path="erstellung/uebersicht" element={<UebersichtPage />} />
					<Route
						path="erstellung/lieferscheine"
						element={<LieferscheinePage />}
					/>
					<Route
						path="erstellung/lieferscheine/:id"
						element={<LieferscheinDetailPage />}
					/>
					<Route path="erstellung/angebote" element={<AngebotePage />} />
					<Route
						path="erstellung/angebote/:id"
						element={<AngeboteDetailPage />}
					/>

					{/* Verwaltung Routes */}
					<Route
						path="verwaltung"
						element={<Navigate to="/verwaltung/kunden" replace />}
					/>
					<Route path="verwaltung/kunden" element={<KundenPage />} />
					<Route
						path="verwaltung/kunden/neu"
						element={<KundenStammdatenNeuPage />}
					/>
					<Route
						path="verwaltung/kunden/:id"
						element={<KundenStammdatenDetailPage />}
					/>
					<Route path="verwaltung/kontingente" element={<KontingentePage />} />
					<Route path="verwaltung/mitarbeiter" element={<MitarbeiterPage />} />
					<Route
						path="verwaltung/mitarbeiter/neu"
						element={<MitarbeiterNeuPage />}
					/>
					<Route
						path="verwaltung/mitarbeiter/:id"
						element={<MitarbeiterDetailPage />}
					/>

					{/* System Routes */}
					<Route
						path="system"
						element={<Navigate to="/system/firmeneinstellungen" replace />}
					/>
					<Route
						path="system/firmeneinstellungen"
						element={<FirmeneinstellungenPage />}
					/>
					<Route path="system/feedback" element={<FeedbackPage />} />
					<Route path="system/standards" element={<StandardsPage />} />
					<Route
						path="system/doku-kategorien"
						element={<DokuKategorienPage />}
					/>

					{/* Catch-all for any other authenticated paths not matched above */}
					{/* This effectively means any path like /foo/bar if signed in will redirect to dashboard */}
					<Route path="*" element={<Navigate to="/" replace />} />
				</Route>
			</Routes>
		</BrowserRouter>
	);
}
