import {
	<PERSON>,
	Card<PERSON>ontent,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
} from "@/components/_shared/Card";
import { Skeleton } from "@/components/_shared/Skeleton";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import { formatCurrency, formatHours } from "@/lib/utils/formatUtils";
import { TrendingUp } from "lucide-react";

interface PerformanceData {
	stunden: number;
	umsatz: number;
	anzahlLeistungen: number;
	anzahlAnfahrten: number;
}

interface CompactPerformanceCardProps {
	tagesLeistung: PerformanceData | null;
	monatsLeistung: PerformanceData | null;
	isLoading: boolean;
}

export function CompactPerformanceCard({
	tagesLeistung,
	monatsLeistung,
	isLoading,
}: CompactPerformanceCardProps) {
	if (isLoading) {
		return (
			<Card className="border-0 shadow-md bg-gray-800/40 overflow-hidden">
				<CardHeader className="p-3 border-b border-gray-700/50">
					<Skeleton className="h-6 w-48" />
				</CardHeader>
				<CardContent className="p-0">
					<div className="p-4">
						{[...Array(2)].map((_, i) => (
							<div key={i} className="flex justify-between py-2">
								<Skeleton className="h-5 w-32" />
								<Skeleton className="h-5 w-16" />
							</div>
						))}
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card className="border-0 shadow-md bg-gray-800/40 overflow-hidden">
			<CardHeader className="p-3 border-b border-gray-700/50">
				<CardTitle className="text-sm font-medium flex items-center">
					<TrendingUp className="w-4 h-4 mr-1.5" />
					Leistungsübersicht
				</CardTitle>
			</CardHeader>
			<CardContent className="p-0">
				<Table>
					<TableHeader>
						<TableRow className="hover:bg-transparent border-b border-gray-700/50">
							<TableHead className="w-[40%] px-3 py-3 text-xs font-medium text-gray-400">
								Zeitraum
							</TableHead>
							<TableHead className="px-3 py-3 text-xs font-medium text-gray-400 text-right">
								Stunden
							</TableHead>
							<TableHead className="px-3 py-3 text-xs font-medium text-gray-400 text-right">
								Umsatz
							</TableHead>
							<TableHead className="px-3 py-3 text-xs font-medium text-gray-400 text-right">
								Leistungen
							</TableHead>
							<TableHead className="px-3 py-3 text-xs font-medium text-gray-400 text-right">
								Anfahrten
							</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						<TableRow className="border-b border-gray-700/50 hover:bg-gray-700/30">
							<TableCell className="px-3 py-2 text-xs font-medium">
								Heute
							</TableCell>
							{tagesLeistung && tagesLeistung.anzahlLeistungen > 0 ? (
								<>
									<TableCell className="px-3 py-2 text-xs text-right">
										{formatHours(tagesLeistung.stunden)}
									</TableCell>
									<TableCell className="px-3 py-2 text-xs text-right">
										{formatCurrency(tagesLeistung.umsatz)}
									</TableCell>
									<TableCell className="px-3 py-2 text-xs text-right">
										{tagesLeistung.anzahlLeistungen}
									</TableCell>
									<TableCell className="px-3 py-2 text-xs text-right">
										{tagesLeistung.anzahlAnfahrten}
									</TableCell>
								</>
							) : (
								<TableCell
									className="px-3 py-2 text-xs text-gray-400 italic text-right"
									colSpan={4}
								>
									Für den ausgewählten Zeitraum wurden keine Daten gefunden.
								</TableCell>
							)}
						</TableRow>
						<TableRow className="border-b border-gray-700/50 last:border-b-0 hover:bg-gray-700/30">
							<TableCell className="px-3 py-2 text-xs font-medium">
								Monat
							</TableCell>
							{monatsLeistung && monatsLeistung.anzahlLeistungen > 0 ? (
								<>
									<TableCell className="px-3 py-2 text-xs text-right">
										{formatHours(monatsLeistung.stunden)}
									</TableCell>
									<TableCell className="px-3 py-2 text-xs text-right">
										{formatCurrency(monatsLeistung.umsatz)}
									</TableCell>
									<TableCell className="px-3 py-2 text-xs text-right">
										{monatsLeistung.anzahlLeistungen}
									</TableCell>
									<TableCell className="px-3 py-2 text-xs text-right">
										{monatsLeistung.anzahlAnfahrten}
									</TableCell>
								</>
							) : (
								<TableCell
									className="px-3 py-2 text-xs text-gray-400 italic text-right"
									colSpan={4}
								>
									Für den ausgewählten Zeitraum wurden keine Daten gefunden.
								</TableCell>
							)}
						</TableRow>
					</TableBody>
				</Table>
			</CardContent>
		</Card>
	);
}
