import { Button } from "@/components/_shared/Button";
import { EmptyState } from "@/components/layout/EmptyState";
import { FileText, Search } from "lucide-react";

interface AngeboteEmptyStateProps {
	searchTerm: string;
	filterStatus:
		| "all"
		| "entwurf"
		| "fertig"
		| "abgeschickt"
		| "abgelehnt"
		| "angenommen"
		| "bezahlt";
	filterZeitraum: "all" | "last7" | "last30" | "month" | "lastMonth" | "custom";
	filterKundeId?: string;
	onResetFilters: () => void;
}

export function AngeboteEmptyState({
	searchTerm,
	filterStatus,
	filterZeitraum,
	filterKundeId = "all",
	onResetFilters,
}: AngeboteEmptyStateProps) {
	const hasFilters =
		searchTerm !== "" ||
		filterStatus !== "all" ||
		filterZeitraum !== "all" ||
		filterKundeId !== "all";

	if (hasFilters) {
		return (
			<EmptyState
				icon={<Search className="h-12 w-12" />}
				title="Keine Angebote gefunden"
				message="Es wurden keine Angebote gefunden, die den aktuellen Filterkriterien entsprechen. Versuchen Sie, die Filter anzupassen oder zurückzusetzen."
				actions={
					<Button variant="outline" onClick={onResetFilters} size="sm">
						Filter zurücksetzen
					</Button>
				}
			/>
		);
	}

	return (
		<EmptyState
			icon={<FileText className="h-12 w-12" />}
			title="Keine Angebote vorhanden"
			message="Es wurden noch keine Angebote erstellt. Erstellen Sie ein neues Angebot, um Ihre Leistungen anzubieten."
		/>
	);
}
