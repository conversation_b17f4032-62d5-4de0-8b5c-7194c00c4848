import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/_shared/select";
import { AppDialogLayout } from "@/components/layout/AppDialogLayout";
import { useMutation, useQuery } from "convex/react";
import { Plus, User } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface NewAngebotDialogProps {
	isOpen: boolean;
	onClose: () => void;
}

export function NewAngebotDialog({ isOpen, onClose }: NewAngebotDialogProps) {
	const [kundeId, setKundeId] = useState<Id<"kunden"> | "">("");

	const kunden = useQuery(api.verwaltung.kunden.list) || [];
	const createAngebot = useMutation(api.erstellung.angebot.create);

	const handleSubmit = async () => {
		if (!kundeId) {
			toast.error("Bitte wählen Sie einen Kunden aus.");
			return;
		}

		try {
			const angebotId = await createAngebot({
				kundenId: kundeId,
				positionen: [],
				gueltigBis: Date.now() + 30 * 24 * 60 * 60 * 1000,
				gesamtsummeNetto: 0,
				gesamtsummeBrutto: 0,
			});

			toast.success("Angebot-Entwurf erfolgreich erstellt.");
			resetForm();
			onClose();

			// Zur Detailseite navigieren (immer mit ID da es ein Entwurf ist)
			if (angebotId) {
				window.location.href = `/erstellung/angebote/${angebotId}`;
			}
		} catch (error) {
			toast.error("Fehler beim Erstellen des Angebots.");
		}
	};

	const resetForm = () => {
		setKundeId("");
	};

	return (
		<AppDialogLayout
			isOpen={isOpen}
			onClose={onClose}
			title="Neues Angebot erstellen"
			maxWidth="md"
			footerAction={{
				label: "Leeren Entwurf erstellen",
				onClick: handleSubmit,
				icon: <Plus className="h-3.5 w-3.5" />,
				disabled: !kundeId,
			}}
		>
			<div className="space-y-4">
				<div className="space-y-2">
					<label
						htmlFor="kundeId"
						className="text-sm font-medium text-gray-300 flex items-center gap-2"
					>
						<User className="h-4 w-4 text-gray-400" />
						Kunde
					</label>
					<Select
						value={kundeId}
						onValueChange={(value: string) => {
							setKundeId(value as Id<"kunden">);
						}}
					>
						<SelectTrigger id="kundeId" className="w-full">
							<SelectValue placeholder="Kunde auswählen" />
						</SelectTrigger>
						<SelectContent>
							{kunden.map((kunde) => (
								<SelectItem key={kunde._id} value={kunde._id}>
									{kunde.name}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				<div className="text-sm text-gray-400 bg-gray-800/50 p-3 rounded-lg">
					Nach der Erstellung können Sie Angebotspositionen hinzufügen und das
					Angebot bearbeiten.
				</div>
			</div>
		</AppDialogLayout>
	);
}
