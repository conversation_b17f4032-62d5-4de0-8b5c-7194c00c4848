import { Doc } from "@/../convex/_generated/dataModel";
import { formatCurrency, formatNumber } from "@/lib/utils/formatUtils";
import {
	Document,
	Font,
	Image,
	Page,
	StyleSheet,
	Text,
	View,
} from "@react-pdf/renderer";

// Fonts etc. are assumed to be registered globally or are inherited.
// Using same styles as Lieferschein for consistency.
Font.register({
	family: "Roboto",
	fonts: [
		{
			src: "https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-regular-webfont.ttf",
			fontWeight: 400,
		},
		{
			src: "https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-medium-webfont.ttf",
			fontWeight: 500,
		},
		{
			src: "https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-bold-webfont.ttf",
			fontWeight: 700,
		},
	],
});

const styles = StyleSheet.create({
	page: {
		paddingHorizontal: 30,
		paddingTop: 30,
		paddingBottom: 60,
		fontFamily: "Roboto",
		fontSize: 10,
		color: "#333333",
		backgroundColor: "#FFFFFF",
	},
	headerContainer: {
		flexDirection: "row",
		justifyContent: "space-between",
		alignItems: "center",
		marginBottom: 15,
		borderBottomWidth: 0.5,
		borderBottomColor: "#DDDDDD",
		paddingBottom: 8,
	},
	headerTextContainer: {
		flex: 1,
		flexDirection: "column",
	},
	headerLogoContainer: {
		width: 200,
		height: 100,
		justifyContent: "center",
		alignItems: "center",
	},
	logo: {
		width: 200,
		height: 100,
		objectFit: "contain",
	},
	title: {
		fontSize: 16,
		fontWeight: 700,
		color: "#333333",
		marginBottom: 4,
	},
	subtitle: {
		fontSize: 10,
		color: "#555555",
		marginBottom: 2,
	},
	section: {
		marginTop: 8,
		marginBottom: 8,
	},
	sectionTitle: {
		fontSize: 12,
		fontWeight: 700,
		color: "#444444",
		marginBottom: 6,
		paddingBottom: 3,
		borderBottomWidth: 0.5,
		borderBottomColor: "#DDDDDD",
	},
	table: {
		display: "flex",
		width: "auto",
		borderStyle: "solid",
		borderWidth: 0.5,
		borderColor: "#DDDDDD",
		marginBottom: 8,
	},
	tableRowHeader: {
		flexDirection: "row",
		backgroundColor: "#F5F5F5",
		fontWeight: 700,
		borderBottomWidth: 0.5,
		borderBottomColor: "#DDDDDD",
		alignItems: "stretch",
		minHeight: 18,
	},
	tableRow: {
		flexDirection: "row",
		borderBottomWidth: 0.5,
		borderBottomColor: "#DDDDDD",
		alignItems: "stretch",
		minHeight: 18,
	},
	tableCol: {
		padding: 4,
		fontSize: 9,
		color: "#333333",
	},
	tableColLast: {},
	colPos: { width: "8%" },
	colTitel: { width: "42%" },
	colMenge: { width: "10%", textAlign: "right" },
	colEinheit: { width: "10%" },
	colEinzelpreis: { width: "15%", textAlign: "right" },
	colGesamt: { width: "15%", textAlign: "right" },
	textMuted: {
		fontSize: 8,
		color: "#777777",
		marginTop: 1,
		lineHeight: 1.3,
	},
	summaryContainer: {
		flexDirection: "row",
		justifyContent: "flex-end",
		marginTop: 10,
	},
	summaryBox: {
		width: "40%",
		borderTopWidth: 1,
		borderTopColor: "#DDDDDD",
		paddingTop: 5,
	},
	summaryRow: {
		flexDirection: "row",
		justifyContent: "space-between",
		paddingVertical: 2,
	},
	summaryLabel: { fontWeight: 400 },
	summaryValue: { textAlign: "right", fontWeight: 400 },
	summaryDiscount: {
		color: "#666666",
	},
	summaryTotal: {
		marginTop: 4,
		paddingTop: 4,
		borderTopWidth: 0.5,
		borderTopColor: "#DDDDDD",
	},
	legalText: {
		marginTop: 12,
		padding: 10,
		backgroundColor: "#F5F5F5",
		borderRadius: 5,
		marginBottom: 8,
	},
	legalTextContent: {
		fontSize: 8,
		color: "#666666",
		lineHeight: 1.4,
	},
	bemerkung: {
		marginTop: 12,
		padding: 10,
		backgroundColor: "#F9F9F9",
		borderRadius: 5,
		marginBottom: 8,
	},
	bemerkungTitle: {
		fontWeight: 700,
		marginBottom: 5,
	},
	signatureContainer: {
		marginTop: 40,
		marginBottom: 20,
		paddingHorizontal: 0,
	},
	signatureLine: {
		borderTopWidth: 0.5,
		borderTopColor: "#AAAAAA",
		width: "45%",
		marginBottom: 3,
	},
	signatureText: {
		fontSize: 8,
		color: "#777777",
	},
	footer: {
		position: "absolute",
		bottom: 25,
		left: 30,
		right: 30,
		fontSize: 8,
		textAlign: "center",
		color: "#888888",
		borderTopWidth: 0.5,
		borderTopColor: "#DDDDDD",
		paddingTop: 6,
	},
	pageNumber: {
		position: "absolute",
		fontSize: 8,
		bottom: 8,
		left: 0,
		right: 30,
		textAlign: "right",
		color: "#888888",
	},
});

type AngebotPDFProps = {
	angebot: Doc<"kunden_angebote"> & { kundeName: string };
	includeHeader?: boolean;
	includeFooter?: boolean;
	showLogo?: boolean;
	includeLegalText?: boolean;
	includeSignatureField?: boolean;
	logoUrl?: string;
	firmenName?: string;
	firmenFusszeileText?: string;
	legalText?: string;
	signatureText?: string;
	formatCurrency: (amount: number) => string;
	// Company information
	companyInfo?: {
		firmenname: string;
		strasse: string;
		plz: string;
		ort: string;
		land?: string;
		telefon?: string;
		email?: string;
		website?: string;
		umsatzsteuerID?: string;
		handelsregisternummer?: string;
		geschaeftsfuehrer?: string;
		zahlungsbedingungen?: string;
		zahlungszielTage?: number;
	};
	// Customer information
	customerInfo?: {
		name: string;
		strasse: string;
		plz: string;
		ort: string;
		land?: string;
	};
};

// Helper function to render multi-line text
const renderMultilineText = (text: string, style: any) => {
	const lines = text.split("\n").filter((line) => line.trim() !== "");
	return lines.map((line, index) => (
		<Text key={index} style={style}>
			{line}
		</Text>
	));
};

export const AngebotPDFDocument = ({
	angebot,
	includeHeader = true,
	includeFooter = true,
	showLogo = true,
	includeLegalText = true,
	includeSignatureField = true,
	logoUrl,
	firmenName = "innov8-IT",
	firmenFusszeileText = "© innov8-IT",
	legalText = "",
	signatureText = "Ort / Datum / Unterschrift / Stempel",
	formatCurrency,
	companyInfo,
	customerInfo,
}: AngebotPDFProps) => {
	const documentTitle = angebot.istKorrektur
		? `Angebot (Korrektur) ${angebot.nummer}`
		: `Angebot ${angebot.nummer}`;

	const subtotal = angebot.positionen.reduce((acc, pos) => {
		return acc + pos.menge * pos.einzelpreis;
	}, 0);

	const taxAmount = subtotal * 0.19;
	const totalAmount = subtotal + taxAmount;

	const displayNettoAmount = angebot.gesamtsummeNetto || subtotal;
	const displayBruttoAmount = angebot.gesamtsummeBrutto || totalAmount;
	const displayTaxAmount = displayBruttoAmount - displayNettoAmount;

	return (
		<Document author={firmenName} title={documentTitle}>
			<Page size="A4" style={styles.page}>
				{includeHeader && (
					<View style={styles.headerContainer}>
						<View style={styles.headerTextContainer}>
							<Text style={styles.title}>{documentTitle}</Text>
							<Text style={styles.subtitle}>Kunde: {angebot.kundeName}</Text>
							<Text style={styles.subtitle}>
								Datum: {new Date(angebot.erstelltAm).toLocaleDateString()}
							</Text>
							<Text style={styles.subtitle}>
								Gültig bis: {new Date(angebot.gueltigBis).toLocaleDateString()}
							</Text>
						</View>
						<View style={styles.headerLogoContainer}>
							{showLogo && logoUrl ? (
								<Image style={styles.logo} src={logoUrl} />
							) : showLogo ? (
								<Text
									style={{
										fontSize: 14,
										color: "#007AFF",
										fontWeight: "bold",
									}}
								>
									innov<Text style={{ color: "#50E3C2" }}>8</Text>-IT
								</Text>
							) : null}
						</View>
					</View>
				)}

				{/* Company and Customer Information */}
				{(companyInfo || customerInfo) && (
					<View style={{ marginTop: 20, marginBottom: 20 }}>
						<View
							style={{ flexDirection: "row", justifyContent: "space-between" }}
						>
							{/* Company Information */}
							{companyInfo && (
								<View style={{ width: "45%" }}>
									<Text
										style={{
											fontSize: 10,
											fontWeight: "bold",
											marginBottom: 5,
										}}
									>
										Absender:
									</Text>
									<Text style={{ fontSize: 9, lineHeight: 1.3 }}>
										{companyInfo.firmenname}
									</Text>
									<Text style={{ fontSize: 9, lineHeight: 1.3 }}>
										{companyInfo.strasse}
									</Text>
									<Text style={{ fontSize: 9, lineHeight: 1.3 }}>
										{companyInfo.plz} {companyInfo.ort}
									</Text>
									{companyInfo.land && (
										<Text style={{ fontSize: 9, lineHeight: 1.3 }}>
											{companyInfo.land}
										</Text>
									)}
									{companyInfo.telefon && (
										<Text
											style={{ fontSize: 9, lineHeight: 1.3, marginTop: 3 }}
										>
											Tel: {companyInfo.telefon}
										</Text>
									)}
									{companyInfo.email && (
										<Text style={{ fontSize: 9, lineHeight: 1.3 }}>
											E-Mail: {companyInfo.email}
										</Text>
									)}
									{companyInfo.website && (
										<Text style={{ fontSize: 9, lineHeight: 1.3 }}>
											Web: {companyInfo.website}
										</Text>
									)}
								</View>
							)}

							{/* Customer Information */}
							{customerInfo && (
								<View style={{ width: "45%" }}>
									<Text
										style={{
											fontSize: 10,
											fontWeight: "bold",
											marginBottom: 5,
										}}
									>
										Empfänger:
									</Text>
									<Text style={{ fontSize: 9, lineHeight: 1.3 }}>
										{customerInfo.name}
									</Text>
									<Text style={{ fontSize: 9, lineHeight: 1.3 }}>
										{customerInfo.strasse}
									</Text>
									<Text style={{ fontSize: 9, lineHeight: 1.3 }}>
										{customerInfo.plz} {customerInfo.ort}
									</Text>
									{customerInfo.land && (
										<Text style={{ fontSize: 9, lineHeight: 1.3 }}>
											{customerInfo.land}
										</Text>
									)}
								</View>
							)}
						</View>
					</View>
				)}

				{/* Angebotspositionen */}
				<View style={styles.section} wrap={false}>
					<Text style={styles.sectionTitle}>Angebotspositionen</Text>
					<View style={styles.table}>
						<View fixed style={[styles.tableRow, styles.tableRowHeader]}>
							<Text style={[styles.tableCol, styles.colPos]}>Pos.</Text>
							<Text style={[styles.tableCol, styles.colTitel]}>
								Bezeichnung
							</Text>
							<Text style={[styles.tableCol, styles.colMenge]}>Menge</Text>
							<Text style={[styles.tableCol, styles.colEinheit]}>Einheit</Text>
							<Text style={[styles.tableCol, styles.colEinzelpreis]}>
								Einzelpreis
							</Text>
							<Text
								style={[styles.tableCol, styles.colGesamt, styles.tableColLast]}
							>
								Gesamt
							</Text>
						</View>
						{angebot.positionen.map((pos, index) => (
							<View key={pos.id} style={styles.tableRow}>
								<Text style={[styles.tableCol, styles.colPos]}>
									{index + 1}
								</Text>
								<View style={[styles.tableCol, styles.colTitel]}>
									<Text>{pos.titel}</Text>
									{pos.beschreibung && (
										<View style={styles.textMuted}>
											{renderMultilineText(pos.beschreibung, styles.textMuted)}
										</View>
									)}
								</View>
								<Text style={[styles.tableCol, styles.colMenge]}>
									{formatNumber(pos.menge)}
								</Text>
								<Text style={[styles.tableCol, styles.colEinheit]}>
									{pos.einheit}
								</Text>
								<Text style={[styles.tableCol, styles.colEinzelpreis]}>
									{formatCurrency(pos.einzelpreis)}
								</Text>
								<Text
									style={[
										styles.tableCol,
										styles.colGesamt,
										styles.tableColLast,
									]}
								>
									{formatCurrency(pos.menge * pos.einzelpreis)}
								</Text>
							</View>
						))}
					</View>
				</View>

				{/* Summary */}
				<View style={styles.summaryContainer}>
					<View style={styles.summaryBox}>
						<View style={styles.summaryRow}>
							<Text style={styles.summaryLabel}>Zwischensumme (Netto):</Text>
							<Text style={styles.summaryValue}>
								{formatCurrency(displayNettoAmount)}
							</Text>
						</View>
						<View style={styles.summaryRow}>
							<Text style={styles.summaryLabel}>
								MwSt. (
								{companyInfo?.standardUmsatzsteuer ||
									angebot.umsatzsteuer ||
									19}
								%):
							</Text>
							<Text style={styles.summaryValue}>
								{formatCurrency(displayTaxAmount)}
							</Text>
						</View>
						<View style={[styles.summaryRow, styles.summaryTotal]}>
							<Text style={styles.summaryLabel}>Gesamtsumme (Brutto):</Text>
							<Text style={styles.summaryValue}>
								{formatCurrency(displayBruttoAmount)}
							</Text>
						</View>
					</View>
				</View>

				{/* Payment Terms */}
				{companyInfo?.zahlungsbedingungen && (
					<View style={{ marginTop: 15, marginBottom: 10 }}>
						<Text style={{ fontSize: 10, fontWeight: "bold", marginBottom: 5 }}>
							Zahlungsbedingungen:
						</Text>
						<Text style={{ fontSize: 9, lineHeight: 1.4 }}>
							{companyInfo.zahlungsbedingungen}
						</Text>
						{companyInfo.zahlungszielTage && (
							<Text style={{ fontSize: 9, lineHeight: 1.4, marginTop: 2 }}>
								Zahlungsziel: {companyInfo.zahlungszielTage} Tage nach
								Rechnungsdatum
							</Text>
						)}
					</View>
				)}

				{/* Legal Text */}
				{includeLegalText && legalText && (
					<View style={styles.legalText}>
						<Text style={styles.legalTextContent}>{legalText}</Text>
					</View>
				)}

				{/* Bemerkung */}
				{angebot.bemerkung && (
					<View style={styles.bemerkung}>
						<Text style={styles.bemerkungTitle}>Bemerkung:</Text>
						<View>
							{renderMultilineText(angebot.bemerkung, {
								fontSize: 10,
								lineHeight: 1.4,
							})}
						</View>
					</View>
				)}

				{/* Unterschriftsfeld */}
				{includeSignatureField && (
					<View style={styles.signatureContainer}>
						<View style={styles.signatureLine}></View>
						<Text style={styles.signatureText}>{signatureText}</Text>
					</View>
				)}

				{includeFooter && (
					<Text style={styles.footer}>{firmenFusszeileText}</Text>
				)}

				<Text
					style={styles.pageNumber}
					render={({ pageNumber, totalPages }) =>
						`Seite ${pageNumber} von ${totalPages}`
					}
				/>
			</Page>
		</Document>
	);
};
