import { Doc } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import {
	<PERSON>,
	CardContent,
	CardH<PERSON><PERSON>,
	CardTitle,
} from "@/components/_shared/Card";
import { Input } from "@/components/_shared/Input";
import { Label } from "@/components/_shared/Label";
import { Textarea } from "@/components/_shared/Textarea";
import { formatCurrency } from "@/lib/utils/formatUtils";
import { zodResolver } from "@hookform/resolvers/zod";
import { Plus, Trash2 } from "lucide-react";
import { useEffect } from "react";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import * as z from "zod";

const positionSchema = z.object({
	id: z.string(),
	titel: z.string().min(1, "Titel ist erforderlich"),
	beschreibung: z.string().optional(),
	menge: z.coerce.number().min(0.1, "Menge muss > 0 sein"),
	einheit: z.string().min(1, "Einheit ist erforderlich"),
	einzelpreis: z.coerce.number(),
});

const positionFormSchema = z.object({
	positionen: z
		.array(positionSchema)
		.min(1, "Es muss mindestens eine Position vorhanden sein"),
});

type PositionFormData = z.infer<typeof positionFormSchema>;

interface CompactPositionFormProps {
	initialData?: { positionen: Doc<"kunden_angebote">["positionen"] };
	onSubmit: (data: {
		positionen: any[];
		gesamtsummeNetto: number;
		gesamtsummeBrutto: number;
	}) => void;
	isSubmitting: boolean;
	formId: string;
	onChangesDetected?: (hasChanges: boolean) => void;
}

export function CompactPositionForm({
	initialData,
	onSubmit,
	isSubmitting,
	formId,
	onChangesDetected,
}: CompactPositionFormProps) {
	const {
		register,
		control,
		handleSubmit,
		watch,
		formState: { errors, isDirty },
	} = useForm<PositionFormData>({
		resolver: zodResolver(positionFormSchema),
		defaultValues: initialData || {
			positionen: [],
		},
	});

	// Notify parent about changes
	useEffect(() => {
		onChangesDetected?.(isDirty);
	}, [isDirty, onChangesDetected]);

	const { fields, append, remove } = useFieldArray({
		control,
		name: "positionen",
	});

	const positionen = watch("positionen");

	const subtotal = positionen.reduce((acc, pos) => {
		const menge = Number(pos.menge) || 0;
		const einzelpreis = Number(pos.einzelpreis) || 0;
		return acc + menge * einzelpreis;
	}, 0);

	const taxAmount = subtotal * 0.19;
	const total = subtotal + taxAmount;

	const handleFormSubmit = (data: PositionFormData) => {
		onSubmit({
			positionen: data.positionen,
			gesamtsummeNetto: subtotal,
			gesamtsummeBrutto: total,
		});
	};

	return (
		<form
			id={formId}
			onSubmit={handleSubmit(handleFormSubmit)}
			className="space-y-4"
		>
			<Card>
				<CardHeader className="p-4 pb-2">
					<div className="flex items-center justify-between">
						<CardTitle className="text-base font-medium">
							Angebotspositionen
						</CardTitle>
						<Button
							type="button"
							size="sm"
							onClick={() =>
								append({
									id: crypto.randomUUID(),
									titel: "",
									beschreibung: "",
									einheit: "Stk",
									menge: 1,
									einzelpreis: 0,
								})
							}
							className="gap-1"
						>
							<Plus className="h-3.5 w-3.5" />
							Position
						</Button>
					</div>
				</CardHeader>
				<CardContent className="p-4 pt-0">
					<div className="space-y-2">
						{fields.length === 0 && (
							<div className="text-center py-8 text-gray-400 text-sm">
								Noch keine Positionen hinzugefügt.
								<br />
								Klicken Sie auf "Position" um zu beginnen.
							</div>
						)}
						{fields.map((field, index) => (
							<div
								key={field.id}
								className="border border-gray-600 rounded-lg p-3 bg-gray-800/50"
							>
								{/* Title and Description */}
								<div className="grid grid-cols-[1fr_auto] gap-2 mb-2">
									<div className="space-y-1">
										<Input
											{...register(`positionen.${index}.titel`)}
											placeholder="Titel der Position"
											className="text-sm font-medium"
										/>
										<Textarea
											{...register(`positionen.${index}.beschreibung`)}
											placeholder="Beschreibung (optional) - Unterstützt Absätze"
											className="text-xs text-gray-400 min-h-[60px] resize-y"
											rows={2}
										/>
									</div>
									<Button
										type="button"
										variant="outline"
										size="sm"
										onClick={() => remove(index)}
										className="h-8 w-8 p-0 text-red-400 hover:text-red-300"
									>
										<Trash2 className="h-3.5 w-3.5" />
									</Button>
								</div>

								{/* Quantity, Unit, Price */}
								<div className="grid grid-cols-3 gap-2">
									<div>
										<Label
											htmlFor={`menge-${index}`}
											className="text-xs text-gray-400"
										>
											Menge
										</Label>
										<Input
											{...register(`positionen.${index}.menge`)}
											type="number"
											step="0.1"
											placeholder="1"
											className="text-sm"
										/>
									</div>
									<div>
										<Label
											htmlFor={`einheit-${index}`}
											className="text-xs text-gray-400"
										>
											Einheit
										</Label>
										<Input
											{...register(`positionen.${index}.einheit`)}
											placeholder="Stk"
											className="text-sm"
										/>
									</div>
									<div>
										<Label
											htmlFor={`preis-${index}`}
											className="text-xs text-gray-400"
										>
											Einzelpreis (€)
										</Label>
										<Input
											{...register(`positionen.${index}.einzelpreis`)}
											type="number"
											step="0.01"
											placeholder="0.00"
											className="text-sm"
										/>
									</div>
								</div>

								{/* Position total */}
								<div className="text-right mt-2 text-sm text-gray-400">
									{formatCurrency(
										(Number(positionen[index]?.menge) || 0) *
											(Number(positionen[index]?.einzelpreis) || 0),
									)}
								</div>
							</div>
						))}
					</div>
					{errors.positionen && (
						<p className="text-red-500 text-xs mt-2">
							{errors.positionen.message}
						</p>
					)}
				</CardContent>
			</Card>

			{/* Totals Summary */}
			<Card>
				<CardHeader className="p-4 pb-2">
					<CardTitle className="text-base font-medium">
						Zusammenfassung
					</CardTitle>
				</CardHeader>
				<CardContent className="p-4 pt-0">
					<div className="space-y-2 text-sm">
						<div className="flex justify-between">
							<span>Zwischensumme (Netto)</span>
							<span>{formatCurrency(subtotal)}</span>
						</div>
						<div className="flex justify-between">
							<span>Umsatzsteuer (19%)</span>
							<span>{formatCurrency(taxAmount)}</span>
						</div>
						<div className="flex justify-between font-bold text-base border-t border-gray-600 pt-2">
							<span>Gesamtsumme (Brutto)</span>
							<span>{formatCurrency(total)}</span>
						</div>
					</div>
				</CardContent>
			</Card>
		</form>
	);
}
