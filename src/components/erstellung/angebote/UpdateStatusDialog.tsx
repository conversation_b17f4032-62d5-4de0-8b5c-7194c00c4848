import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import { Label } from "@/components/_shared/Label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/_shared/Select";
import { AppDialogLayout } from "@/components/layout/AppDialogLayout";
import { useMutation } from "convex/react";
import { CheckCircle, Mail, X } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface UpdateStatusDialogProps {
	isOpen: boolean;
	onClose: () => void;
	angebotId: Id<"kunden_angebote">;
	currentStatus: string;
	angebotNummer?: string;
	onStatusUpdated?: () => void;
}

const statusOptions = [
	{ value: "fertig", label: "Fertig", icon: CheckCircle, color: "text-green-400" },
	{ value: "abgeschickt", label: "Abgeschickt", icon: Mail, color: "text-blue-300" },
	{ value: "abgelehnt", label: "Abgelehnt", icon: X, color: "text-red-400" },
	{ value: "angenommen", label: "Angenommen", icon: CheckCircle, color: "text-green-300" },
	{ value: "bezahlt", label: "Bezahlt", icon: CheckCircle, color: "text-emerald-400" },
];

export function UpdateStatusDialog({
	isOpen,
	onClose,
	angebotId,
	currentStatus,
	angebotNummer,
	onStatusUpdated,
}: UpdateStatusDialogProps) {
	const [selectedStatus, setSelectedStatus] = useState<string>(currentStatus);
	const [isSubmitting, setIsSubmitting] = useState(false);

	const updateStatus = useMutation(api.erstellung.angebot.updateStatus);

	const handleSubmit = async () => {
		if (selectedStatus === currentStatus) {
			onClose();
			return;
		}

		setIsSubmitting(true);
		try {
			await updateStatus({ id: angebotId, status: selectedStatus });
			
			const statusLabel = statusOptions.find(opt => opt.value === selectedStatus)?.label || selectedStatus;
			toast.success(`Angebot-Status erfolgreich auf "${statusLabel}" aktualisiert.`);
			
			onStatusUpdated?.();
			onClose();
		} catch (error) {
			toast.error(
				`Fehler beim Aktualisieren des Status: ${
					error instanceof Error ? error.message : "Unbekannter Fehler"
				}`,
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	// Filter out statuses that are not valid transitions
	const availableStatuses = statusOptions.filter(option => {
		// Don't allow changing back to fertig if already in a later status
		if (option.value === "fertig" && currentStatus !== "fertig") {
			return false;
		}
		return true;
	});

	const selectedStatusOption = statusOptions.find(opt => opt.value === selectedStatus);

	return (
		<AppDialogLayout
			isOpen={isOpen}
			onClose={onClose}
			title="Angebot-Status aktualisieren"
			description={`Aktualisieren Sie den Status für Angebot ${angebotNummer || ""}. Der Status hilft dabei, den Fortschritt des Angebots zu verfolgen.`}
			icon={<CheckCircle className="h-4 w-4 text-blue-500" />}
			maxWidth="sm"
			footerAction={{
				label: isSubmitting ? "Aktualisieren..." : "Status aktualisieren",
				onClick: handleSubmit,
				disabled: isSubmitting || selectedStatus === currentStatus,
			}}
		>
			<div className="space-y-4">
				<div>
					<Label htmlFor="status">Neuer Status</Label>
					<Select value={selectedStatus} onValueChange={setSelectedStatus}>
						<SelectTrigger className="w-full">
							<SelectValue placeholder="Status auswählen" />
						</SelectTrigger>
						<SelectContent>
							{availableStatuses.map((option) => {
								const Icon = option.icon;
								return (
									<SelectItem key={option.value} value={option.value}>
										<div className="flex items-center gap-2">
											<Icon className={`h-4 w-4 ${option.color}`} />
											<span>{option.label}</span>
										</div>
									</SelectItem>
								);
							})}
						</SelectContent>
					</Select>
				</div>

				{selectedStatusOption && (
					<div className="p-3 bg-gray-800/50 rounded-md">
						<div className="flex items-center gap-2 mb-2">
							<selectedStatusOption.icon className={`h-4 w-4 ${selectedStatusOption.color}`} />
							<span className="font-medium">{selectedStatusOption.label}</span>
						</div>
						<p className="text-sm text-gray-400">
							{selectedStatus === "fertig" && "Das Angebot ist fertiggestellt und kann versendet werden."}
							{selectedStatus === "abgeschickt" && "Das Angebot wurde an den Kunden versendet."}
							{selectedStatus === "abgelehnt" && "Das Angebot wurde vom Kunden abgelehnt."}
							{selectedStatus === "angenommen" && "Das Angebot wurde vom Kunden angenommen."}
							{selectedStatus === "bezahlt" && "Das Angebot wurde bezahlt und ist abgeschlossen."}
						</p>
					</div>
				)}

				{selectedStatus !== currentStatus && (
					<div className="p-3 bg-yellow-900/20 border border-yellow-700/50 rounded-md">
						<p className="text-sm text-yellow-300">
							<strong>Hinweis:</strong> Diese Änderung wird sofort wirksam und kann nicht rückgängig gemacht werden.
						</p>
					</div>
				)}
			</div>
		</AppDialogLayout>
	);
}
