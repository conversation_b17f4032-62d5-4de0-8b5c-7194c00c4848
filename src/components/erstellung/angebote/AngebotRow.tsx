import { Doc, Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import { TableCell, TableRow } from "@/components/_shared/Table";
import { formatCurrency } from "@/lib/utils/formatUtils";
import {
	AlertTriangle,
	CheckCircle,
	ChevronDown,
	ChevronUp,
	FileText,
	Mail,
	Trash2,
	X,
} from "lucide-react";
import { Link } from "react-router-dom";

interface AngebotRowProps {
	angebot: Doc<"kunden_angebote"> & {
		kundeName: string;
		erstelltAmFormatiert: string;
		hatKorrektur: boolean;
	};
	korrekturen: Array<
		Doc<"kunden_angebote"> & {
			kundeName: string;
			erstelltAmFormatiert: string;
			hatKorrektur: boolean;
		}
	>;
	original:
		| (Doc<"kunden_angebote"> & {
				kundeName: string;
				erstelltAmFormatiert: string;
				hatKorrektur: boolean;
		  })
		| null;
	onDelete: (id: Id<"kunden_angebote">) => void;
	isExpanded: boolean;
	onToggleExpand: () => void;
}

export function AngebotRow({
	angebot,
	korrekturen,
	original,
	onDelete,
	isExpanded,
	onToggleExpand,
}: AngebotRowProps) {
	// Bestimme, ob es Korrekturen oder ein Original gibt, die angezeigt werden können
	const hasRelatedDocuments = korrekturen.length > 0 || original !== null;

	return (
		<>
			{/* Hauptzeile */}
			<TableRow className="border-b border-gray-800">
				<TableCell className="font-medium">
					<div className="flex items-center">
						{hasRelatedDocuments && (
							<Button
								variant="ghost"
								size="icon"
								className="h-6 w-6 mr-2 text-gray-400 hover:text-gray-300"
								onClick={onToggleExpand}
								title={isExpanded ? "Einklappen" : "Ausklappen"}
							>
								{isExpanded ? (
									<ChevronUp className="h-4 w-4" />
								) : (
									<ChevronDown className="h-4 w-4" />
								)}
							</Button>
						)}
						<span>
							{angebot.nummer || "Entwurf"}
							{angebot.istKorrektur && (
								<span className="ml-2 text-xs text-orange-400">
									(Korrektur)
								</span>
							)}
						</span>
					</div>
				</TableCell>
				<TableCell>{angebot.kundeName}</TableCell>
				<TableCell>{angebot.erstelltAmFormatiert}</TableCell>
				<TableCell>
					{(() => {
						// Show "Korrigiert" status for original quotes that have corrections
						if (angebot.hatKorrektur && !angebot.istKorrektur) {
							return (
								<div className="flex items-center text-orange-400">
									<AlertTriangle className="h-4 w-4 mr-1" />
									<span>Korrigiert</span>
								</div>
							);
						}

						// Show status based on the actual status field
						switch (angebot.status) {
							case "entwurf":
								return (
									<div className="flex items-center text-blue-400">
										<FileText className="h-4 w-4 mr-1" />
										<span>Entwurf</span>
									</div>
								);
							case "fertig":
								return (
									<div className="flex items-center text-green-400">
										<CheckCircle className="h-4 w-4 mr-1" />
										<span>Fertig</span>
									</div>
								);
							case "abgeschickt":
								return (
									<div className="flex items-center text-blue-300">
										<Mail className="h-4 w-4 mr-1" />
										<span>Abgeschickt</span>
									</div>
								);
							case "abgelehnt":
								return (
									<div className="flex items-center text-red-400">
										<X className="h-4 w-4 mr-1" />
										<span>Abgelehnt</span>
									</div>
								);
							case "angenommen":
								return (
									<div className="flex items-center text-green-300">
										<CheckCircle className="h-4 w-4 mr-1" />
										<span>Angenommen</span>
									</div>
								);
							case "bezahlt":
								return (
									<div className="flex items-center text-emerald-400">
										<CheckCircle className="h-4 w-4 mr-1" />
										<span>Bezahlt</span>
									</div>
								);
							default:
								return <span className="text-gray-400">Unbekannt</span>;
						}
					})()}
				</TableCell>
				<TableCell className="text-right">
					{formatCurrency(angebot.gesamtsummeBrutto)}
				</TableCell>
				<TableCell>
					<div className="flex justify-center gap-1">
						{/* Anzeigen/Bearbeiten Button */}
						<Link
							to={`/erstellung/angebote/${angebot.status === "fertig" && angebot.nummer ? angebot.nummer : angebot._id}`}
						>
							<Button
								variant="ghost"
								size="icon"
								className="h-8 w-8 text-gray-400 hover:text-blue-400"
								title="Anzeigen"
							>
								<FileText className="h-4 w-4" />
								<span className="sr-only">Anzeigen</span>
							</Button>
						</Link>

						{/* Korrektur Button - für alle finalisierten Angebote */}
						{angebot.status === "fertig" && (
							<Link
								to={`/erstellung/angebote/${angebot.nummer || angebot._id}`}
								state={{ openCorrectionDialog: true }}
							>
								<Button
									variant="ghost"
									size="icon"
									className="h-8 w-8 text-gray-400 hover:text-orange-400"
									title="Korrektur erstellen"
								>
									<AlertTriangle className="h-4 w-4" />
									<span className="sr-only">Korrektur</span>
								</Button>
							</Link>
						)}

						{/* Lösch-Button nur für Entwürfe */}
						{angebot.status === "entwurf" && (
							<Button
								variant="ghost"
								size="icon"
								className="h-8 w-8 text-gray-400 hover:text-red-400"
								title="Löschen"
								onClick={() => onDelete(angebot._id)}
							>
								<Trash2 className="h-4 w-4" />
								<span className="sr-only">Löschen</span>
							</Button>
						)}
					</div>
				</TableCell>
			</TableRow>

			{/* Untergeordnete Zeilen (Korrekturen und Original) */}
			{isExpanded && (
				<>
					{/* Andere Korrekturen (außer der neuesten) */}
					{korrekturen.map((korrektur) => (
						<TableRow
							key={korrektur._id}
							className="border-b border-gray-800 bg-gray-800/20"
						>
							<TableCell className="font-medium pl-10">
								{korrektur.nummer || "Entwurf"}
								<span className="ml-2 text-xs text-orange-400">
									(Korrektur)
								</span>
							</TableCell>
							<TableCell>{korrektur.kundeName}</TableCell>
							<TableCell>{korrektur.erstelltAmFormatiert}</TableCell>
							<TableCell>
								{korrektur.status === "entwurf" ? (
									<div className="flex items-center text-blue-400">
										<FileText className="h-4 w-4 mr-1" />
										<span>Entwurf</span>
									</div>
								) : (
									<span className="text-green-400">Fertig</span>
								)}
							</TableCell>
							<TableCell className="text-right">
								{formatCurrency(korrektur.gesamtsummeBrutto)}
							</TableCell>
							<TableCell>
								<div className="flex justify-center gap-1">
									<Link
										to={`/erstellung/angebote/${korrektur.nummer || korrektur._id}`}
									>
										<Button
											variant="ghost"
											size="icon"
											className="h-8 w-8 text-gray-400 hover:text-blue-400"
											title="Anzeigen"
										>
											<FileText className="h-4 w-4" />
											<span className="sr-only">Anzeigen</span>
										</Button>
									</Link>

									{/* Korrektur Button für finalisierte Korrekturen */}
									{korrektur.status === "fertig" && (
										<Link
											to={`/erstellung/angebote/${korrektur.nummer || korrektur._id}`}
											state={{ openCorrectionDialog: true }}
										>
											<Button
												variant="ghost"
												size="icon"
												className="h-8 w-8 text-gray-400 hover:text-orange-400"
												title="Korrektur erstellen"
											>
												<AlertTriangle className="h-4 w-4" />
												<span className="sr-only">Korrektur</span>
											</Button>
										</Link>
									)}

									{korrektur.status === "entwurf" && (
										<Button
											variant="ghost"
											size="icon"
											className="h-8 w-8 text-gray-400 hover:text-red-400"
											title="Löschen"
											onClick={() => onDelete(korrektur._id)}
										>
											<Trash2 className="h-4 w-4" />
											<span className="sr-only">Löschen</span>
										</Button>
									)}
								</div>
							</TableCell>
						</TableRow>
					))}

					{/* Original-Dokument (ganz unten) */}
					{original && (
						<TableRow
							key={original._id}
							className="border-b border-gray-800 bg-gray-800/30"
						>
							<TableCell className="font-medium pl-10">
								{original.nummer || "Entwurf"}
								<span className="ml-2 text-xs text-gray-400">(Original)</span>
							</TableCell>
							<TableCell>{original.kundeName}</TableCell>
							<TableCell>{original.erstelltAmFormatiert}</TableCell>
							<TableCell>
								<span className="text-green-400">Fertig</span>
							</TableCell>
							<TableCell className="text-right">
								{formatCurrency(original.gesamtsummeBrutto)}
							</TableCell>
							<TableCell>
								<div className="flex justify-center gap-1">
									<Link
										to={`/erstellung/angebote/${original.nummer || original._id}`}
									>
										<Button
											variant="ghost"
											size="icon"
											className="h-8 w-8 text-gray-400 hover:text-blue-400"
											title="Anzeigen"
										>
											<FileText className="h-4 w-4" />
											<span className="sr-only">Anzeigen</span>
										</Button>
									</Link>
									{/* Korrektur-Button für das Original */}
									<Link
										to={`/erstellung/angebote/${original.nummer || original._id}`}
										state={{ openCorrectionDialog: true }}
									>
										<Button
											variant="ghost"
											size="icon"
											className="h-8 w-8 text-gray-400 hover:text-orange-400"
											title="Korrektur erstellen"
										>
											<AlertTriangle className="h-4 w-4" />
											<span className="sr-only">Korrektur</span>
										</Button>
									</Link>
								</div>
							</TableCell>
						</TableRow>
					)}
				</>
			)}
		</>
	);
}
