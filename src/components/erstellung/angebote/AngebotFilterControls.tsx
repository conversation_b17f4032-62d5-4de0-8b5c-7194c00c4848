import { api } from "@/../convex/_generated/api";
import type { Doc } from "@/../convex/_generated/dataModel";
import {
	type FilterConfig,
	type FilterOption,
	GenericFilterControls,
} from "@/components/layout/GenericFilterControls";
import { useQuery } from "convex/react";
import { useEffect } from "react";

interface AngebotFilter {
	status: "all" | "entwurf" | "fertig";
	zeitraum: "all" | "last7" | "last30" | "month" | "lastMonth" | "custom";
	startDatum: string;
	endDatum: string;
	kundeId: string;
}

interface AngebotFilterControlsProps {
	filter: AngebotFilter;
	searchTerm: string;
	onFilterChange: (key: keyof AngebotFilter, value: string) => void;
	onSearchTermChange: (value: string) => void;
	onResetAllFilters?: () => void;
}

export function AngebotFilterControls({
	filter,
	searchTerm,
	onFilterChange,
	onSearchTermChange,
	onResetAllFilters,
}: AngebotFilterControlsProps) {
	const kunden = useQuery(api.verwaltung.kunden.list) || [];

	useEffect(() => {
		if (
			filter.zeitraum === "all" &&
			filter.startDatum === "" &&
			filter.endDatum === ""
		) {
			onFilterChange("zeitraum", "month");
		}
	}, [filter.zeitraum, filter.startDatum, filter.endDatum, onFilterChange]);

	const kundenOptions: FilterOption[] = [
		{ value: "all", label: "Alle Kunden" },
		...kunden.map((kunde: Doc<"kunden">) => ({
			value: kunde._id,
			label: kunde.name,
		})),
	];

	const statusOptions: FilterOption[] = [
		{ value: "all", label: "Alle Status" },
		{ value: "entwurf", label: "Entwurf" },
		{ value: "fertig", label: "Fertig" },
		{ value: "abgeschickt", label: "Abgeschickt" },
		{ value: "abgelehnt", label: "Abgelehnt" },
		{ value: "angenommen", label: "Angenommen" },
		{ value: "bezahlt", label: "Bezahlt" },
	];

	const zeitraumOptions: FilterOption[] = [
		{ value: "all", label: "Alle Zeiträume" },
		{ value: "last7", label: "Letzte 7 Tage" },
		{ value: "last30", label: "Letzte 30 Tage" },
		{ value: "month", label: "Dieser Monat" },
		{ value: "lastMonth", label: "Letzter Monat" },
		{ value: "custom", label: "Benutzerdefiniert" },
	];

	const filtersConfig: FilterConfig[] = [
		{
			type: "select",
			id: "angebotKundeFilter",
			value: filter.kundeId,
			onChange: (value) => onFilterChange("kundeId", value),
			options: kundenOptions,
			placeholder: "Alle Kunden",
			triggerClassName: "h-8 text-xs bg-gray-800 border-gray-700",
			minWidth: "150px",
		},
		{
			type: "select",
			id: "angebotStatusFilter",
			value: filter.status,
			onChange: (value) => onFilterChange("status", value),
			options: statusOptions,
			placeholder: "Status",
			triggerClassName: "h-8 text-xs bg-gray-800 border-gray-700",
			minWidth: "130px",
		},
		{
			type: "dateRange",
			idPrefix: "angebotZeitraum",
			zeitraumValue: filter.zeitraum,
			startDateValue: filter.startDatum,
			endDateValue: filter.endDatum,
			onZeitraumChange: (value) => onFilterChange("zeitraum", value),
			onStartDateChange: (value) => onFilterChange("startDatum", value),
			onEndDateChange: (value) => onFilterChange("endDatum", value),
			zeitraumOptions: zeitraumOptions,
			selectTriggerClassName: "h-8 text-xs bg-gray-800 border-gray-700",
			customDateInputClassName:
				"h-8 text-xs w-auto bg-gray-800 border-gray-700",
			minWidth: "130px",
		},
		{
			type: "search",
			id: "angebotSearch",
			value: searchTerm,
			onChange: onSearchTermChange,
			placeholder: "Suchen...",
			className: "flex-grow",
			inputClassName: "h-8 text-xs pl-8 w-full bg-gray-800 border-gray-700",
		},
	];

	return (
		<GenericFilterControls
			filters={filtersConfig}
			onResetAll={onResetAllFilters}
		/>
	);
}
