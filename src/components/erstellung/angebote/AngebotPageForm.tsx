import { api } from "@/../convex/_generated/api";
import { Doc } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/components/_shared/Card";
import { Input } from "@/components/_shared/Input";
import { Label } from "@/components/_shared/Label";
import { Textarea } from "@/components/_shared/Textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/_shared/select";
import { formatCurrency } from "@/lib/utils/formatUtils";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQuery } from "convex/react";
import { Plus, Trash2 } from "lucide-react";
import { useEffect } from "react";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import * as z from "zod";

const positionSchema = z.object({
	id: z.string(),
	titel: z.string().min(1, "Titel ist erforderlich"),
	beschreibung: z.string().optional(),
	menge: z.coerce.number().min(0.1, "Menge muss > 0 sein"),
	einheit: z.string().min(1, "Einheit ist erforderlich"),
	einzelpreis: z.coerce.number(),
});

const angebotSchema = z.object({
	kundenId: z.string().min(1, "Kunde ist erforderlich"),
	gueltigBis: z.coerce.date({
		required_error: "Gültigkeitsdatum ist erforderlich",
	}),
	bemerkung: z.string().optional(),
	positionen: z
		.array(positionSchema)
		.min(1, "Es muss mindestens eine Position vorhanden sein"),
});

type AngebotFormData = z.infer<typeof angebotSchema>;

interface AngebotPageFormProps {
	initialData?: Partial<Doc<"kunden_angebote">>;
	onSubmit: (data: any) => void;
	isSubmitting: boolean;
	formId: string;
}

export function AngebotPageForm({
	initialData,
	onSubmit,
	isSubmitting,
	formId,
}: AngebotPageFormProps) {
	const kunden = useQuery(api.verwaltung.kunden.list) || [];
	const {
		register,
		control,
		handleSubmit,
		watch,
		setValue,
		formState: { errors },
	} = useForm<AngebotFormData>({
		resolver: zodResolver(angebotSchema),
		defaultValues: initialData
			? {
					...initialData,
					gueltigBis: new Date(initialData.gueltigBis ?? new Date()),
				}
			: {
					kundenId: "",
					gueltigBis: new Date(new Date().setDate(new Date().getDate() + 14)),
					positionen: [],
				},
	});

	const { fields, append, remove } = useFieldArray({
		control,
		name: "positionen",
	});

	const positionen = watch("positionen");
	const totals = positionen.reduce(
		(acc: { netto: number }, pos: { menge: number; einzelpreis: number }) => {
			const menge = Number(pos.menge) || 0;
			const einzelpreis = Number(pos.einzelpreis) || 0;
			const posTotal = menge * einzelpreis;
			acc.netto += posTotal;
			return acc;
		},
		{ netto: 0 },
	);
	const umsatzsteuerValue = totals.netto * 0.19;
	const brutto = totals.netto + umsatzsteuerValue;

	const handleFormSubmit = (data: AngebotFormData) => {
		const finalData = {
			...data,
			gueltigBis: new Date(data.gueltigBis).getTime(),
			gesamtsummeNetto: totals.netto,
			gesamtsummeBrutto: brutto,
		};
		onSubmit(finalData);
	};

	return (
		<form
			id={formId}
			onSubmit={handleSubmit(handleFormSubmit)}
			className="space-y-4"
		>
			<Card>
				<CardHeader>
					<CardTitle>Grunddaten</CardTitle>
				</CardHeader>
				<CardContent className="grid md:grid-cols-2 gap-4">
					<div>
						<Label htmlFor="kundenId">Kunde</Label>
						<Controller
							name="kundenId"
							control={control}
							render={({ field }) => (
								<Select
									onValueChange={field.onChange}
									defaultValue={field.value}
								>
									<SelectTrigger>
										<SelectValue placeholder="Kunde auswählen" />
									</SelectTrigger>
									<SelectContent>
										{kunden.map((k) => (
											<SelectItem key={k._id} value={k._id}>
												{k.name}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							)}
						/>
						{errors.kundenId && (
							<p className="text-red-500 text-xs mt-1">
								{errors.kundenId.message}
							</p>
						)}
					</div>
					<div>
						<Label htmlFor="gueltigBis">Gültig bis</Label>
						<Input type="date" {...register("gueltigBis")} />
						{errors.gueltigBis && (
							<p className="text-red-500 text-xs mt-1">
								{errors.gueltigBis.message}
							</p>
						)}
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader className="flex flex-row items-center justify-between">
					<CardTitle>Positionen</CardTitle>
					<Button
						type="button"
						size="sm"
						onClick={() =>
							append({
								id: crypto.randomUUID(),
								titel: "",
								beschreibung: "",
								einheit: "Stk",
								menge: 1,
								einzelpreis: 0,
							})
						}
					>
						<Plus className="h-4 w-4 mr-2" /> Position hinzufügen
					</Button>
				</CardHeader>
				<CardContent>
					<div className="space-y-3">
						{fields.map((field, index) => (
							<div key={field.id} className="border rounded-md p-3 space-y-2">
								<div className="grid grid-cols-[1fr_80px_80px_120px_auto] gap-2 items-start">
									<div className="space-y-1">
										<Input
											{...register(`positionen.${index}.titel`)}
											placeholder="Titel"
										/>
										<Input
											{...register(`positionen.${index}.beschreibung`)}
											placeholder="Beschreibung (optional)"
											className="text-xs text-gray-400"
										/>
									</div>
									<Input
										{...register(`positionen.${index}.menge`)}
										type="number"
										step="0.1"
										placeholder="Menge"
									/>
									<Input
										{...register(`positionen.${index}.einheit`)}
										placeholder="Einheit"
									/>
									<Input
										{...register(`positionen.${index}.einzelpreis`)}
										type="number"
										step="0.01"
										placeholder="Preis/Einheit"
									/>
									<Button
										type="button"
										variant="destructive"
										size="icon"
										onClick={() => remove(index)}
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								</div>
							</div>
						))}
					</div>
					{errors.positionen && (
						<p className="text-red-500 text-xs mt-2">
							{errors.positionen.message}
						</p>
					)}
				</CardContent>
			</Card>

			<div className="flex justify-end">
				<div className="w-full max-w-sm space-y-2 text-sm">
					<div className="flex justify-between">
						<span>Zwischensumme (Netto)</span>{" "}
						<span>{formatCurrency(totals.netto)}</span>
					</div>
					<div className="flex justify-between">
						<span>Umsatzsteuer (19%)</span>{" "}
						<span>{formatCurrency(umsatzsteuerValue)}</span>
					</div>
					<div className="flex justify-between font-bold text-base">
						<span>Gesamtsumme (Brutto)</span>{" "}
						<span>{formatCurrency(brutto)}</span>
					</div>
				</div>
			</div>

			<Card>
				<CardHeader>
					<CardTitle>Bemerkung</CardTitle>
				</CardHeader>
				<CardContent>
					<Textarea
						{...register("bemerkung")}
						placeholder="Optionale Bemerkungen zum Angebot..."
					/>
				</CardContent>
			</Card>
		</form>
	);
}
