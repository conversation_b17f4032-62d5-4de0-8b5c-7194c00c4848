import { Button } from "@/components/_shared/Button";
import {
	<PERSON>,
	<PERSON>Content,
	Card<PERSON><PERSON><PERSON>,
	CardTitle,
} from "@/components/_shared/Card";
import { Input } from "@/components/_shared/Input";
import { Label } from "@/components/_shared/Label";

import { forwardRef, useCallback, useImperativeHandle, useState } from "react";
import { toast } from "sonner";

interface GrunddatenCardProps {
	angebot: {
		kundenId: string;
		kundeName: string;
		gueltigBis: number;
		status: string;
	};
	onUpdate: (data: {
		gueltigBis: number;
	}) => Promise<void>;
	onChangesDetected?: (hasChanges: boolean) => void;
}

export interface GrunddatenCardRef {
	save: () => Promise<void>;
	hasChanges: boolean;
}

export const GrunddatenCard = forwardRef<
	GrunddatenCardRef,
	GrunddatenCardProps
>(({ angebot, onUpdate, onChangesDetected }, ref) => {
	const [gueltigBis, setGueltigBis] = useState(
		new Date(angebot.gueltigBis).toISOString().split("T")[0],
	);
	const [hasChanges, setHasChanges] = useState(false);

	const handleSave = useCallback(async () => {
		try {
			await onUpdate({
				gueltigBis: new Date(gueltigBis).getTime(),
			});
			const newHasChanges = false;
			setHasChanges(newHasChanges);
			onChangesDetected?.(newHasChanges);
			toast.success("Grunddaten erfolgreich aktualisiert");
		} catch (error) {
			toast.error("Fehler beim Speichern der Grunddaten");
		}
	}, [gueltigBis, onUpdate, onChangesDetected]);

	const handleFieldChange = () => {
		const newHasChanges = true;
		setHasChanges(newHasChanges);
		onChangesDetected?.(newHasChanges);
	};

	const isEditable = angebot.status === "entwurf";

	useImperativeHandle(ref, () => ({
		save: handleSave,
		hasChanges,
	}));

	return (
		<Card className="shadow-lg border-0">
			<CardHeader className="p-3 pb-2">
				<CardTitle className="text-base font-medium">Grunddaten</CardTitle>
			</CardHeader>
			<CardContent className="p-3 pt-0">
				<div className="space-y-3">
					{/* Gültig bis */}
					<div>
						<Label
							htmlFor="gueltigBis"
							className="text-sm font-medium text-gray-300"
						>
							Gültig bis
						</Label>
						<Input
							id="gueltigBis"
							type="date"
							value={gueltigBis}
							onChange={(e) => {
								setGueltigBis(e.target.value);
								handleFieldChange();
							}}
							disabled={!isEditable}
							className="text-sm"
						/>
					</div>
				</div>
			</CardContent>
		</Card>
	);
});

GrunddatenCard.displayName = "GrunddatenCard";
