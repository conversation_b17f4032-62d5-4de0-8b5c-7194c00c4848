import { useClerk, useUser } from "@clerk/clerk-react";
import {
	BarChart3,
	Briefcase,
	ChevronDown,
	ChevronUp,
	Home,
	LayoutDashboard,
	LogOut,
	Menu,
	MessageSquare,
	Settings,
	UserCircle,
	Users,
	X,
} from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { FeedbackButton } from "../system/feedback/FeedbackButton";

type NavItem = {
	label: string;
	path?: string;
	icon?: React.ReactNode;
	children?: { label: string; path: string }[];
};

export function Navigation() {
	const location = useLocation();
	const [openNavMenu, setOpenNavMenu] = useState<string | null>(null);
	const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
	const [openMobileCategory, setOpenMobileCategory] = useState<string | null>(
		null,
	);
	const navRef = useRef<HTMLElement>(null);

	// Get user info from Clerk
	const { isLoaded, isSignedIn, user } = useUser();
	// Get Clerk functions
	const { openUserProfile, signOut } = useClerk();

	// Get user name and profile image
	const userName = user
		? `${user.firstName || ""} ${user.lastName || ""}`.trim()
		: "";
	const userProfileImage = user?.imageUrl;

	// Fallback to email if name is not available
	const userDisplayName =
		userName ||
		user?.primaryEmailAddress?.emailAddress ||
		user?.emailAddresses[0]?.emailAddress;

	const toggleNavMenu = (label: string) => {
		setOpenNavMenu((prevOpenMenu) => (prevOpenMenu === label ? null : label));
	};

	const toggleMobileCategory = (label: string) => {
		setOpenMobileCategory((prevCategory) =>
			prevCategory === label ? null : label,
		);
	};

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (navRef.current && !navRef.current.contains(event.target as Node)) {
				setOpenNavMenu(null);
			}
		};
		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, []);

	useEffect(() => {
		setOpenNavMenu(null);
		setIsMobileMenuOpen(false);
		setOpenMobileCategory(null);
	}, [location.pathname]);

	const isActive = (path?: string, isParent = false) => {
		if (!path) return false;
		if (isParent) {
			return location.pathname.startsWith(path);
		}
		return location.pathname === path;
	};

	// Updated Navigation Structure
	const navItems: NavItem[] = [
		{
			label: "Kunden",
			icon: <Users className="h-4 w-4" />,
			path: "/kunden", // Base path for highlighting
			children: [
				{ label: "Dokumentation", path: "/kunden/doku" },
				{ label: "Termine", path: "/kunden/termine" },
			],
		},
		{
			label: "Erstellung",
			icon: <Briefcase className="h-4 w-4" />,
			path: "/erstellung", // Base path for highlighting
			children: [
				{ label: "Leistung", path: "/erstellung/leistung" },
				{ label: "Übersicht", path: "/erstellung/uebersicht" },
				{ label: "Angebote", path: "/erstellung/angebote" },
				{ label: "Lieferscheine", path: "/erstellung/lieferscheine" },
			],
		},
		{
			label: "Verwaltung",
			icon: <Settings className="h-4 w-4" />,
			path: "/verwaltung", // Base path for highlighting
			children: [
				{ label: "Kunden", path: "/verwaltung/kunden" },
				{ label: "Kontingente", path: "/verwaltung/kontingente" },
				{ label: "Mitarbeiter", path: "/verwaltung/mitarbeiter" },
			],
		},
		{
			label: "System",
			icon: <LayoutDashboard className="h-4 w-4" />,
			path: "/system", // Base path for highlighting
			children: [
				{ label: "Feedback", path: "/system/feedback" },
				{ label: "Standards", path: "/system/standards" },
				{ label: "Doku-Kategorien", path: "/system/doku-kategorien" },
			],
		},
	];

	return (
		<nav
			className="bg-gray-800/80 backdrop-blur-sm shadow-md border-b border-gray-700/50 sticky top-0 z-10"
			ref={navRef}
		>
			<div className="container mx-auto px-4 max-w-7xl">
				<div className="flex h-14 items-center justify-between">
					{/* Left side: Logo/Brand */}
					<div className="flex-shrink-0">
						<Link
							to="/"
							className="text-xl font-bold text-white flex items-center"
						>
							{/* Updated Branding */}
							innov<span className="text-blue-400">8</span>-IT
						</Link>
					</div>

					{/* Center: Navigation Links */}
					<div className="hidden md:flex md:items-center md:space-x-1 lg:space-x-1">
						{navItems.map((item) => (
							<div key={item.label} className="relative group">
								<button
									onClick={() => item.children && toggleNavMenu(item.label)}
									className={`flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors ${
										isActive(item.path, true) ||
										(
											item.children &&
												item.children.some((child) => isActive(child.path))
										)
											? "bg-primary/20 text-primary"
											: "text-gray-300 hover:bg-gray-700/70 hover:text-white"
									}`}
								>
									{item.icon && <span className="mr-1.5">{item.icon}</span>}
									<span>{item.label}</span>
									{item.children &&
										(openNavMenu === item.label ? (
											<ChevronUp className="ml-1 h-3.5 w-3.5" />
										) : (
											<ChevronDown className="ml-1 h-3.5 w-3.5" />
										))}
								</button>
								{item.children && openNavMenu === item.label && (
									<div className="absolute left-0 mt-1 w-40 origin-top-left rounded-md bg-gray-800 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-10 animate-in fade-in duration-100 border border-gray-700/50">
										<div
											className="py-1"
											role="menu"
											aria-orientation="vertical"
											aria-labelledby="options-menu"
										>
											{item.children.map((child) => (
												<Link
													key={child.path}
													to={child.path}
													className={`block px-4 py-2 text-sm ${
														isActive(child.path)
															? "bg-primary/10 text-primary"
															: "text-gray-300 hover:bg-gray-700/70 hover:text-white"
													}`}
													role="menuitem"
												>
													{child.label}
												</Link>
											))}
										</div>
									</div>
								)}
							</div>
						))}
					</div>

					{/* Right side: User Profile and Logout */}
					<div className="hidden md:flex items-center gap-2 relative">
						{/* Show only when Clerk is loaded and user is signed in */}
						{isLoaded && isSignedIn && (
							<>
								{/* User Profile Button */}
								<button
									onClick={() => openUserProfile()}
									className="flex items-center gap-2 text-sm text-gray-300 hover:text-white p-2 rounded-md hover:bg-gray-700/70"
									title="Kontoeinstellungen öffnen"
								>
									{/* User Profile Image */}
									{userProfileImage ? (
										<img
											src={userProfileImage}
											alt="Profilbild"
											className="w-7 h-7 rounded-full object-cover border border-gray-700"
										/>
									) : (
										<div className="w-7 h-7 rounded-full bg-gray-700 flex items-center justify-center text-gray-300">
											<UserCircle className="h-4 w-4" />
										</div>
									)}
									{/* User Name */}
									<span
										className="max-w-[150px] truncate text-xs"
										title={userDisplayName ?? undefined}
									>
										{userDisplayName}
									</span>
								</button>

								{/* Feedback Button */}
								<FeedbackButton className="p-2 rounded-md text-gray-300 hover:text-yellow-300 hover:bg-gray-700/70" />

								{/* Direct Logout Button */}
								<button
									onClick={() => signOut({ redirectUrl: "/signin" })}
									className="p-2 rounded-md text-gray-300 hover:text-red-300 hover:bg-gray-700/70"
									title="Abmelden"
								>
									<LogOut className="h-4 w-4" />
								</button>
							</>
						)}
					</div>

					{/* Mobile Menu Button */}
					<div className="md:hidden flex items-center">
						{/* Hamburger Button */}
						<button
							onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
							className="p-2 rounded-md text-gray-300 hover:text-white hover:bg-gray-700/70"
						>
							{isMobileMenuOpen ? (
								<X className="h-6 w-6" />
							) : (
								<Menu className="h-6 w-6" />
							)}
						</button>
					</div>
				</div>
			</div>

			{/* Mobile Menu Panel */}
			{isMobileMenuOpen && (
				<div className="md:hidden bg-gray-800/95 backdrop-blur-sm border-t border-gray-700/50 animate-in fade-in slide-in-from-top-2 duration-200">
					{/* User Info Section (Mobile) */}
					{isLoaded && isSignedIn && (
						<div className="px-4 py-3 border-b border-gray-700/50 flex items-center">
							{/* User Profile Image and Name */}
							<div className="flex items-center flex-1">
								{userProfileImage ? (
									<img
										src={userProfileImage}
										alt="Profilbild"
										className="w-8 h-8 rounded-full object-cover border border-gray-700 mr-3"
									/>
								) : (
									<div className="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center text-gray-300 mr-3">
										<UserCircle className="h-5 w-5" />
									</div>
								)}
								<div>
									<div className="text-sm font-medium text-white">
										{userName}
									</div>
									<div className="text-xs text-gray-400 truncate max-w-[200px]">
										{user?.primaryEmailAddress?.emailAddress}
									</div>
								</div>
							</div>

							{/* Account Settings Button */}
							<button
								onClick={() => openUserProfile()}
								className="p-2 rounded-md text-gray-300 hover:text-white hover:bg-gray-700/70"
								title="Kontoeinstellungen"
							>
								<Settings className="h-5 w-5" />
							</button>

							{/* Feedback Button */}
							<FeedbackButton className="p-2 rounded-md text-gray-300 hover:text-yellow-300 hover:bg-gray-700/70 ml-1" />

							{/* Logout Button */}
							<button
								onClick={() => signOut({ redirectUrl: "/signin" })}
								className="p-2 rounded-md text-gray-300 hover:text-red-300 hover:bg-gray-700/70 ml-1"
								title="Abmelden"
							>
								<LogOut className="h-5 w-5" />
							</button>
						</div>
					)}

					{/* Navigation Menu */}
					<div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
						{navItems.map((item) => (
							<div key={item.label} className="mb-1">
								{/* Category Header */}
								<button
									onClick={() => toggleMobileCategory(item.label)}
									className={`w-full flex items-center justify-between rounded-md px-3 py-2 text-sm font-medium ${
										isActive(item.path, true) ||
										(
											item.children &&
												item.children.some((child) => isActive(child.path))
										)
											? "bg-primary/20 text-primary"
											: "text-gray-200 bg-gray-700/30 hover:bg-gray-700/50"
									}`}
								>
									<div className="flex items-center">
										{item.icon && <span className="mr-2">{item.icon}</span>}
										<span>{item.label}</span>
									</div>
									{item.children &&
										(openMobileCategory === item.label ? (
											<ChevronUp className="h-4 w-4" />
										) : (
											<ChevronDown className="h-4 w-4" />
										))}
								</button>

								{/* Category Children */}
								{item.children && openMobileCategory === item.label && (
									<div className="mt-1 pl-2 border-l border-gray-700/50 ml-3 space-y-1">
										{item.children.map((child) => (
											<Link
												key={child.path}
												to={child.path}
												className={`flex items-center rounded-md px-3 py-2 text-sm ${
													isActive(child.path)
														? "bg-primary/10 text-primary"
														: "text-gray-300 hover:bg-gray-700/70 hover:text-white"
												}`}
												onClick={() => setIsMobileMenuOpen(false)}
											>
												{child.label}
											</Link>
										))}
									</div>
								)}
							</div>
						))}
					</div>
				</div>
			)}
		</nav>
	);
}
