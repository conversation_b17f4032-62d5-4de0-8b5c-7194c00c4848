import { api } from "@/../convex/_generated/api";
import {
	<PERSON>,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/components/_shared/Card";
import { Skeleton } from "@/components/_shared/Skeleton";
import { EmptyState } from "@/components/layout/EmptyState";
import { formatDate, formatHours, formatTime } from "@/lib/utils/formatUtils";
import { useQuery } from "convex/react";
import { Briefcase, Calendar, Clock, Info, User, Users } from "lucide-react";
import { useMemo } from "react";

interface DashboardRecentActivityProps {
	filter: {
		zeitraum: string;
		startDatum: string;
		endDatum: string;
		kundeId: string;
		mitarbeiterId: string;
	};
	isLoading: boolean;
}

// Loading skeleton component
const LoadingSkeleton = () => (
	<Card className="border-0 shadow-md bg-gray-800/40 overflow-hidden">
		<CardHeader className="p-4 border-b border-gray-700/50">
			<Skeleton className="h-6 w-48" />
		</CardHeader>
		<CardContent className="p-0">
			<div className="p-4 space-y-4">
				{[...Array(5)].map((_, i) => (
					<div key={i} className="space-y-2">
						<Skeleton className="h-5 w-full" />
						<Skeleton className="h-4 w-3/4" />
						<Skeleton className="h-4 w-1/2" />
					</div>
				))}
			</div>
		</CardContent>
	</Card>
);

// Empty state wrapper component
const EmptyStateWrapper = () => (
	<Card className="border-dashed border-gray-700 bg-gray-800/30 h-full">
		<CardContent className="py-12">
			<EmptyState
				icon={<Info className="w-10 h-10" />}
				title="Keine Aktivitäten"
				message="Für den ausgewählten Zeitraum wurden keine Aktivitäten gefunden."
			/>
		</CardContent>
	</Card>
);

export function DashboardRecentActivity({
	filter,
	isLoading,
}: DashboardRecentActivityProps) {
	// Fetch all leistungen
	const allLeistungen = useQuery(api.erstellung.leistung.list) || [];

	// Filter and sort leistungen based on the filter criteria
	const recentLeistungen = useMemo(() => {
		if (!allLeistungen) return [];

		// Filter leistungen based on date range and selected filters
		const filteredLeistungen = allLeistungen.filter((leistung) => {
			const leistungDatum = new Date(leistung.startZeit);
			const startDate = new Date(filter.startDatum);
			const endDate = new Date(filter.endDatum);

			// Check if leistung is within date range
			const isInDateRange =
				leistungDatum >= startDate && leistungDatum <= endDate;

			// Check if leistung matches selected kunde
			const matchesKunde =
				filter.kundeId === "all" || leistung.kundenId === filter.kundeId;

			// Check if leistung matches selected mitarbeiter
			const matchesMitarbeiter =
				filter.mitarbeiterId === "all" ||
				leistung.mitarbeiterId === filter.mitarbeiterId;

			return isInDateRange && matchesKunde && matchesMitarbeiter;
		});

		// Sort by date (newest first)
		return filteredLeistungen
			.sort((a, b) => b.startZeit - a.startZeit)
			.slice(0, 10); // Get only the 10 most recent
	}, [allLeistungen, filter]);

	if (isLoading) {
		return <LoadingSkeleton />;
	}

	return (
		<Card className="border-0 shadow-md bg-gray-800/40 overflow-hidden h-full">
			<CardHeader className="p-3 border-b border-gray-700/50">
				<CardTitle className="text-sm font-medium flex items-center">
					<Calendar className="w-4 h-4 mr-1.5" />
					Letzte Aktivitäten
				</CardTitle>
			</CardHeader>
			<CardContent className="p-0">
				{recentLeistungen.length > 0 ? (
					<div className="divide-y divide-gray-700/50">
						{recentLeistungen.map((leistung) => {
							const startDate = new Date(leistung.startZeit);
							const endDate = new Date(leistung.endZeit);

							// Bestimme den Leistungstyp (Remote/Vor-Ort/Vor-Ort (free))
							let leistungsTyp = "Remote";
							if (leistung.mitAnfahrt) {
								leistungsTyp =
									leistung.anfahrtskosten > 0 ? "Vor-Ort" : "Vor-Ort (free)";
							}

							return (
								<div key={leistung._id} className="p-3 hover:bg-gray-700/20">
									<div className="flex justify-between items-start mb-2">
										<div className="flex items-center gap-1.5">
											<Users className="w-3 h-3 text-blue-400" />
											<span className="text-xs font-medium text-gray-200">
												{leistung.kundeName}
											</span>
										</div>
										<div className="text-xs text-gray-400">
											{formatDate(startDate)}
										</div>
									</div>

									<div className="flex justify-between items-center mb-1">
										<div className="flex items-center gap-1.5">
											<User className="w-3 h-3 text-gray-400" />
											<span className="text-xs text-gray-300">
												{leistung.mitarbeiterName}
											</span>
										</div>
										<div className="text-xs text-gray-400">
											{formatHours(leistung.stunden)}
										</div>
									</div>

									<div className="flex justify-between items-center">
										<div className="flex items-center gap-1.5">
											<Clock className="w-3 h-3 text-gray-400" />
											<span className="text-xs text-gray-400">
												{formatTime(startDate)} - {formatTime(endDate)}
											</span>
										</div>
										<div className="text-xs text-gray-400">{leistungsTyp}</div>
									</div>
								</div>
							);
						})}
					</div>
				) : (
					<div className="p-4 text-right text-gray-400 text-sm italic">
						Für den ausgewählten Zeitraum wurden keine Daten gefunden.
					</div>
				)}
			</CardContent>
		</Card>
	);
}
