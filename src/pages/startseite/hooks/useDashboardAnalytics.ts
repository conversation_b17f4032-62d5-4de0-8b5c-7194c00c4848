import { Doc } from "@/../convex/_generated/dataModel"; // Assuming path based on existing imports
import { useMemo } from "react";

// Type definitions
export interface DashboardFilter {
	zeitraum: "month" | "lastMonth" | "year" | "lastYear" | "day";
	startDatum: string;
	endDatum: string;
	kundeId: string;
	mitarbeiterId: string;
}

export interface KundenStatistik {
	kundeId: string;
	kundeName: string;
	stunden: number;
	umsatz: number;
	anzahlLeistungen: number;
	anzahlAnfahrten: number;
}

export interface MitarbeiterStatistik {
	mitarbeiterId: string;
	mitarbeiterName: string;
	stunden: number;
	anzahlLeistungen: number;
}

export interface KontingentProKundeStatistik {
	kundeId: string;
	kundeName: string;
	gesamtStunden: number;
	verbrauchtStunden: number;
	restStunden: number;
	anzahlKontingente: number;
	anzahlLeistungen: number;
	restlaufzeitTage: number; // Anzahl der Tage bis zum Ablauf
}

export interface DashboardDaten {
	gesamtStundenLeistung: number;
	gesamtUmsatz: number;
	anzahlLeistungenGesamt: number;
	aktiveKundenCount: number;
	aktiveMitarbeiterCount: number;
	anzahlAnfahrten: number;
	kundenStatistiken: KundenStatistik[];
	mitarbeiterStatistiken: MitarbeiterStatistik[];
	kontingentStatistiken: KontingentProKundeStatistik[];
	topLeistungenKunden: KundenStatistik[];
	topLeistungenMitarbeiter: MitarbeiterStatistik[];
	tagesLeistung: {
		stunden: number;
		umsatz: number;
		anzahlLeistungen: number;
		anzahlAnfahrten: number;
	};
	monatsLeistung: {
		stunden: number;
		umsatz: number;
		anzahlLeistungen: number;
		anzahlAnfahrten: number;
	};
}

// Define more specific types for data coming from queries, which may include joined fields
type LeistungWithNames = Doc<"kunden_leistungen"> & {
	kundeName: string;
	mitarbeiterName: string;
};

type KontingentWithKundeName = Doc<"kunden_kontingente"> & {
	kundeName: string;
	// restStunden might be calculated by the query or needs to be calculated here
	// If it's not on the object, we'll calculate it from stunden and verbrauchteStunden
};

interface UseDashboardAnalyticsProps {
	allLeistungen: LeistungWithNames[] | undefined;
	allKontingente: KontingentWithKundeName[] | undefined;
	filter: DashboardFilter;
}

export const useDashboardAnalytics = ({
	allLeistungen,
	allKontingente,
	filter,
}: UseDashboardAnalyticsProps): DashboardDaten | null => {
	return useMemo(() => {
		if (!allLeistungen || !allKontingente) return null;

		const filteredLeistungen = allLeistungen.filter((leistung) => {
			const leistungDatum = new Date(leistung.startZeit);
			const startDate = new Date(filter.startDatum);
			const endDate = new Date(filter.endDatum);

			const isInDateRange =
				leistungDatum >= startDate && leistungDatum <= endDate;

			const matchesKunde =
				filter.kundeId === "all" || leistung.kundenId === filter.kundeId;

			const matchesMitarbeiter =
				filter.mitarbeiterId === "all" ||
				leistung.mitarbeiterId === filter.mitarbeiterId;

			return isInDateRange && matchesKunde && matchesMitarbeiter;
		});

		let gesamtStundenLeistung = 0;
		let gesamtUmsatz = 0;
		let anzahlLeistungenGesamt = filteredLeistungen.length;
		let anzahlAnfahrten = 0;

		const aktiveKundenLeistung = new Map<string, boolean>();
		const aktiveMitarbeiter = new Map<string, boolean>();

		// Calculate today's date for "Heute" row
		const heute = new Date();
		heute.setHours(0, 0, 0, 0);

		// Calculate the first day of the selected period for "Monat" row
		// This will be the start of the selected filter period
		const filterStartDate = new Date(filter.startDatum);
		filterStartDate.setHours(0, 0, 0, 0);

		let tagesLeistung = {
			stunden: 0,
			umsatz: 0,
			anzahlLeistungen: 0,
			anzahlAnfahrten: 0,
		};

		let monatsLeistung = {
			stunden: 0,
			umsatz: 0,
			anzahlLeistungen: 0,
			anzahlAnfahrten: 0,
		};

		const kundenStatsMap = new Map<
			string,
			{
				kundeName: string;
				stunden: number;
				umsatz: number;
				anzahlLeistungen: number;
				anzahlAnfahrten: number;
			}
		>();

		const mitarbeiterStatsMap = new Map<
			string,
			{
				mitarbeiterName: string;
				stunden: number;
				anzahlLeistungen: number;
			}
		>();

		filteredLeistungen.forEach((leistung) => {
			const leistungDatum = new Date(leistung.startZeit);
			leistungDatum.setHours(0, 0, 0, 0);

			gesamtStundenLeistung += leistung.stunden;
			gesamtUmsatz += leistung.stunden * leistung.stundenpreis;

			const hatAnfahrt = leistung.mitAnfahrt;
			if (hatAnfahrt) {
				gesamtUmsatz += leistung.anfahrtskosten;
				anzahlAnfahrten++;
			}

			// Calculate "Heute" row - only for today's date
			if (leistungDatum.getTime() === heute.getTime()) {
				tagesLeistung.stunden += leistung.stunden;
				tagesLeistung.umsatz += leistung.stunden * leistung.stundenpreis;
				if (hatAnfahrt) {
					tagesLeistung.umsatz += leistung.anfahrtskosten;
					tagesLeistung.anzahlAnfahrten++;
				}
				tagesLeistung.anzahlLeistungen++;
			}

			// Calculate "Monat" row - for the entire selected filter period
			// Since we're already filtering by the selected period above,
			// all filteredLeistungen belong to the selected period
			monatsLeistung.stunden += leistung.stunden;
			monatsLeistung.umsatz += leistung.stunden * leistung.stundenpreis;
			if (hatAnfahrt) {
				monatsLeistung.umsatz += leistung.anfahrtskosten;
				monatsLeistung.anzahlAnfahrten++;
			}
			monatsLeistung.anzahlLeistungen++;

			aktiveKundenLeistung.set(leistung.kundenId, true);
			aktiveMitarbeiter.set(leistung.mitarbeiterId, true);

			const kundeKey = leistung.kundenId;
			const kundeStats = kundenStatsMap.get(kundeKey) || {
				kundeName: leistung.kundeName,
				stunden: 0,
				umsatz: 0,
				anzahlLeistungen: 0,
				anzahlAnfahrten: 0,
			};
			kundeStats.stunden += leistung.stunden;
			kundeStats.umsatz += leistung.stunden * leistung.stundenpreis;
			if (leistung.mitAnfahrt) {
				kundeStats.umsatz += leistung.anfahrtskosten;
				kundeStats.anzahlAnfahrten++;
			}
			kundeStats.anzahlLeistungen++;
			kundenStatsMap.set(kundeKey, kundeStats);

			const mitarbeiterKey = leistung.mitarbeiterId;
			const mitarbeiterStats = mitarbeiterStatsMap.get(mitarbeiterKey) || {
				mitarbeiterName: leistung.mitarbeiterName,
				stunden: 0,
				anzahlLeistungen: 0,
			};
			mitarbeiterStats.stunden += leistung.stunden;
			mitarbeiterStats.anzahlLeistungen++;
			mitarbeiterStatsMap.set(mitarbeiterKey, mitarbeiterStats);
		});

		const kontingentStatsMap = new Map<
			string,
			{
				kundeName: string;
				gesamtStunden: number;
				verbrauchtStunden: number;
				restStunden: number;
				anzahlKontingente: number;
				anzahlLeistungen: number;
				restlaufzeitTage: number;
			}
		>();

		allKontingente.forEach((kontingent) => {
			const kundeKey = kontingent.kundenId;
			const stats = kontingentStatsMap.get(kundeKey) || {
				kundeName: kontingent.kundeName,
				gesamtStunden: 0,
				verbrauchtStunden: 0,
				restStunden: 0,
				anzahlKontingente: 0,
				anzahlLeistungen: 0,
				restlaufzeitTage: Number.MAX_SAFE_INTEGER, // Start with max value to find minimum
			};

			stats.gesamtStunden += kontingent.stunden;
			stats.verbrauchtStunden += kontingent.verbrauchteStunden;
			// Calculate restStunden as it's not directly on the schema
			stats.restStunden += kontingent.stunden - kontingent.verbrauchteStunden;
			stats.anzahlKontingente++;

			// Calculate remaining days for this kontingent
			const endDate = new Date(kontingent.endDatum);
			const today = new Date();
			today.setHours(0, 0, 0, 0);
			const diffTime = endDate.getTime() - today.getTime();
			const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

			// We want the minimum remaining days for this customer (earliest expiring kontingent)
			if (diffDays < stats.restlaufzeitTage) {
				stats.restlaufzeitTage = diffDays;
			}

			kontingentStatsMap.set(kundeKey, stats);
		});

		// Clean up the restlaufzeitTage for customers with no kontingente
		// (this shouldn't happen in practice, but just in case)
		for (const [kundeKey, stats] of kontingentStatsMap.entries()) {
			if (stats.restlaufzeitTage === Number.MAX_SAFE_INTEGER) {
				stats.restlaufzeitTage = 0;
			}
		}

		// Berechne die Anzahl der Leistungen pro Kontingent-Kunde
		filteredLeistungen.forEach((leistung) => {
			const kundeKey = leistung.kundenId;
			const stats = kontingentStatsMap.get(kundeKey);
			if (stats) {
				stats.anzahlLeistungen++;
			}
		});

		const kundenStatistiken: KundenStatistik[] = Array.from(
			kundenStatsMap.entries(),
		).map(([kundeId, stats]) => ({ kundeId, ...stats }));

		const mitarbeiterStatistiken: MitarbeiterStatistik[] = Array.from(
			mitarbeiterStatsMap.entries(),
		).map(([mitarbeiterId, stats]) => ({ mitarbeiterId, ...stats }));

		const kontingentStatistiken: KontingentProKundeStatistik[] = Array.from(
			kontingentStatsMap.entries(),
		).map(([kundeId, stats]) => ({ kundeId, ...stats }));

		const topLeistungenKunden = [...kundenStatistiken]
			.sort((a, b) => b.stunden - a.stunden)
			.slice(0, 5);

		const topLeistungenMitarbeiter = [...mitarbeiterStatistiken]
			.sort((a, b) => b.stunden - a.stunden)
			.slice(0, 5);

		return {
			gesamtStundenLeistung,
			gesamtUmsatz,
			anzahlLeistungenGesamt,
			aktiveKundenCount: aktiveKundenLeistung.size,
			aktiveMitarbeiterCount: aktiveMitarbeiter.size,
			anzahlAnfahrten,
			kundenStatistiken,
			mitarbeiterStatistiken,
			kontingentStatistiken,
			topLeistungenKunden,
			topLeistungenMitarbeiter,
			tagesLeistung,
			monatsLeistung,
		};
	}, [allLeistungen, allKontingente, filter]);
};
