import { Button } from "@/components/_shared/Button";
import {
	<PERSON>,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/components/_shared/Card";
import { Progress } from "@/components/_shared/Progress";
import { Skeleton } from "@/components/_shared/Skeleton";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import { calculateDateRange, toISODateString } from "@/lib/utils/dateUtils";
import { formatCurrency, formatHours } from "@/lib/utils/formatUtils";
import { Eye, EyeOff, Hourglass, Info, Users } from "lucide-react";
import React, { useState } from "react";
import type { DashboardFilter } from "./hooks/useDashboardAnalytics";

interface KundenStatistik {
	kundeId: string;
	kundeName: string;
	stunden: number;
	umsatz: number;
	anzahlLeistungen: number;
	anzahlAnfahrten: number;
}

interface MitarbeiterStatistik {
	mitarbeiterId: string;
	mitarbeiterName: string;
	stunden: number;
	anzahlLeistungen: number;
}

interface KontingentProKundeStatistik {
	kundeId: string;
	kundeName: string;
	gesamtStunden: number;
	verbrauchtStunden: number;
	restStunden: number;
	anzahlKontingente: number;
	anzahlLeistungen: number;
	restlaufzeitTage: number;
}

interface DashboardDaten {
	gesamtStundenLeistung: number;
	gesamtUmsatz: number;
	anzahlLeistungenGesamt: number;
	aktiveKundenCount: number;
	aktiveMitarbeiterCount: number;
	anzahlAnfahrten: number;
	kundenStatistiken: KundenStatistik[];
	mitarbeiterStatistiken: MitarbeiterStatistik[];
	kontingentStatistiken: KontingentProKundeStatistik[];
	topLeistungenKunden: KundenStatistik[];
	topLeistungenMitarbeiter: MitarbeiterStatistik[];
	tagesLeistung: {
		stunden: number;
		umsatz: number;
		anzahlLeistungen: number;
		anzahlAnfahrten: number;
	};
	monatsLeistung: {
		stunden: number;
		umsatz: number;
		anzahlLeistungen: number;
		anzahlAnfahrten: number;
	};
}

interface DashboardTablesProps {
	dashboardDaten: DashboardDaten | null;
	isLoading: boolean;
	filter: DashboardFilter;
	onFilterChange: (filter: DashboardFilter) => void;
	allKontingente: any[];
	allKunden: any[];
	allLeistungen: any[];
}

const LoadingSkeleton = () => (
	<Card className="border-0 shadow-md bg-gray-800/40 overflow-hidden">
		<CardHeader className="p-4 border-b border-gray-700/50">
			<Skeleton className="h-6 w-48" />
		</CardHeader>
		<CardContent className="p-0">
			<div className="p-4">
				{[...Array(5)].map((_, i) => (
					<div key={i} className="flex justify-between py-2">
						<Skeleton className="h-5 w-32" />
						<Skeleton className="h-5 w-16" />
					</div>
				))}
			</div>
		</CardContent>
	</Card>
);

export function DashboardTables({
	dashboardDaten,
	isLoading,
	filter,
	onFilterChange,
	allKontingente,
	allKunden,
	allLeistungen,
}: DashboardTablesProps) {
	const [showInactiveKontingente, setShowInactiveKontingente] = useState(false);
	const [showTodayRow, setShowTodayRow] = useState(false);
	const [showAllCustomers, setShowAllCustomers] = useState(false);

	const calculateRemainingDays = (endDatum: number): number => {
		const endDate = new Date(endDatum);
		const today = new Date();
		today.setHours(0, 0, 0, 0);
		const diffTime = endDate.getTime() - today.getTime();
		return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
	};

	const renderRemainingDays = (days: number) => (
		<span
			className={`text-xs font-medium ${
				days < 0
					? "text-red-400"
					: days <= 7
						? "text-orange-400"
						: days <= 30
							? "text-yellow-400"
							: "text-green-400"
			}`}
		>
			{days < 0
				? `${Math.abs(days)} Tage überfällig`
				: days === 0
					? "Läuft heute ab"
					: days === 1
						? "1 Tag"
						: `${days} Tage`}
		</span>
	);

	const PerformanceFilter = () => {
		const handleZeitraumChange = (zeitraum: string) => {
			const { startDate, endDate } = calculateDateRange(zeitraum as any);
			const newFilter: DashboardFilter = {
				...filter,
				zeitraum: zeitraum as any,
				startDatum: toISODateString(startDate),
				endDatum: toISODateString(endDate),
			};
			onFilterChange(newFilter);
		};

		return (
			<select
				value={filter.zeitraum}
				onChange={(e) => handleZeitraumChange(e.target.value)}
				className="text-xs bg-gray-800 border border-gray-700 rounded px-2 py-1"
			>
				<option value="month">Aktueller Monat</option>
				<option value="lastMonth">Letzter Monat</option>
				<option value="year">Dieses Jahr</option>
				<option value="lastYear">Letztes Jahr</option>
			</select>
		);
	};

	if (isLoading) {
		return <LoadingSkeleton />;
	}

	const topLeistungenKunden = dashboardDaten?.topLeistungenKunden || [];

	const displayedCustomers = showAllCustomers
		? allKunden
				.map((kunde: any) => {
					const existingData = topLeistungenKunden.find(
						(k) => k.kundeId === kunde._id,
					);
					return (
						existingData || {
							kundeId: kunde._id,
							kundeName: kunde.name,
							stunden: 0,
							umsatz: 0,
							anzahlLeistungen: 0,
							anzahlAnfahrten: 0,
						}
					);
				})
				.sort((a, b) => a.kundeName.localeCompare(b.kundeName))
		: topLeistungenKunden;

	const filteredIndividualKontingente = allKontingente
		.filter((k: any) => {
			if (showInactiveKontingente) return true;
			return k.istAktiv && k.restStunden > 0;
		})
		.sort((a: any, b: any) => {
			if (a.kundeName !== b.kundeName) {
				return a.kundeName.localeCompare(b.kundeName);
			}
			return a.name.localeCompare(b.name);
		});

	const kontingentsByCustomer = filteredIndividualKontingente.reduce(
		(acc: any, k: any) => {
			if (!acc[k.kundeName]) {
				acc[k.kundeName] = [];
			}
			acc[k.kundeName].push(k);
			return acc;
		},
		{},
	);

	const getKontingentCounts = (customerName: string) => {
		const allCustomerKontingente = allKontingente.filter(
			(k: any) => k.kundeName === customerName,
		);
		const aktiv = allCustomerKontingente.filter(
			(k: any) => k.istAktiv && k.restStunden > 0,
		).length;
		const inaktiv = allCustomerKontingente.length - aktiv;
		return { aktiv, inaktiv };
	};

	const getLeistungenCountForKontingent = (kontingentId: string): number => {
		return allLeistungen.filter(
			(leistung: any) =>
				leistung.kontingentId === kontingentId ||
				leistung.kontingentId2 === kontingentId,
		).length;
	};

	return (
		<div className="space-y-4">
			<Card className="border-0 shadow-md bg-gray-800/40 overflow-hidden">
				<CardHeader className="p-3 border-b border-gray-700/50">
					<div className="flex items-center justify-between">
						<CardTitle className="text-sm font-medium flex items-center">
							<Info className="w-4 h-4 mr-1.5" /> Leistungsübersicht
						</CardTitle>
						<div className="flex items-center gap-2">
							<Button
								variant="ghost"
								size="sm"
								onClick={() => setShowTodayRow(!showTodayRow)}
								className="p-1 h-6 w-6"
								title={
									showTodayRow
										? "'Heute' Zeile ausblenden"
										: "'Heute' Zeile anzeigen"
								}
							>
								{showTodayRow ? (
									<Eye className="h-3 w-3" />
								) : (
									<EyeOff className="h-3 w-3" />
								)}
							</Button>
							<PerformanceFilter />
						</div>
					</div>
				</CardHeader>
				<CardContent className="p-0">
					<Table>
						<TableHeader>
							<TableRow className="hover:bg-transparent border-b border-gray-700/50">
								<TableHead className="w-[40%] px-3 py-3 text-xs font-medium text-gray-400">
									Zeitraum
								</TableHead>
								<TableHead className="px-3 py-3 text-xs font-medium text-gray-400 text-right">
									Stunden
								</TableHead>
								<TableHead className="px-3 py-3 text-xs font-medium text-gray-400 text-right">
									Umsatz
								</TableHead>
								<TableHead className="px-3 py-3 text-xs font-medium text-gray-400 text-right">
									Leistungen
								</TableHead>
								<TableHead className="px-3 py-3 text-xs font-medium text-gray-400 text-right">
									Anfahrten
								</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{dashboardDaten?.tagesLeistung &&
							dashboardDaten?.monatsLeistung ? (
								<>
									{showTodayRow && (
										<TableRow className="border-b border-gray-700/50 hover:bg-gray-700/30">
											<TableCell className="px-3 py-2 text-xs font-medium">
												Heute
											</TableCell>
											{dashboardDaten.tagesLeistung.stunden === 0 &&
											dashboardDaten.tagesLeistung.umsatz === 0 &&
											dashboardDaten.tagesLeistung.anzahlLeistungen === 0 &&
											dashboardDaten.tagesLeistung.anzahlAnfahrten === 0 ? (
												<TableCell
													className="px-3 py-2 text-xs text-gray-400 italic text-right"
													colSpan={4}
												>
													Für den ausgewählten Zeitraum wurden keine Daten
													gefunden.
												</TableCell>
											) : (
												<>
													<TableCell className="px-3 py-2 text-xs text-right">
														{formatHours(dashboardDaten.tagesLeistung.stunden)}
													</TableCell>
													<TableCell className="px-3 py-2 text-xs text-right">
														{formatCurrency(
															dashboardDaten.tagesLeistung.umsatz,
														)}
													</TableCell>
													<TableCell className="px-3 py-2 text-xs text-right">
														{dashboardDaten.tagesLeistung.anzahlLeistungen}
													</TableCell>
													<TableCell className="px-3 py-2 text-xs text-right">
														{dashboardDaten.tagesLeistung.anzahlAnfahrten}
													</TableCell>
												</>
											)}
										</TableRow>
									)}
									<TableRow className="border-b border-gray-700/50 hover:bg-gray-700/30">
										<TableCell className="px-3 py-2 text-xs font-medium">
											Monat
										</TableCell>
										{dashboardDaten.monatsLeistung.stunden === 0 &&
										dashboardDaten.monatsLeistung.umsatz === 0 &&
										dashboardDaten.monatsLeistung.anzahlLeistungen === 0 &&
										dashboardDaten.monatsLeistung.anzahlAnfahrten === 0 ? (
											<TableCell
												className="px-3 py-2 text-xs text-gray-400 italic text-right"
												colSpan={4}
											>
												Für den ausgewählten Zeitraum wurden keine Daten
												gefunden.
											</TableCell>
										) : (
											<>
												<TableCell className="px-3 py-2 text-xs text-right">
													{formatHours(dashboardDaten.monatsLeistung.stunden)}
												</TableCell>
												<TableCell className="px-3 py-2 text-xs text-right">
													{formatCurrency(dashboardDaten.monatsLeistung.umsatz)}
												</TableCell>
												<TableCell className="px-3 py-2 text-xs text-right">
													{dashboardDaten.monatsLeistung.anzahlLeistungen}
												</TableCell>
												<TableCell className="px-3 py-2 text-xs text-right">
													{dashboardDaten.monatsLeistung.anzahlAnfahrten}
												</TableCell>
											</>
										)}
									</TableRow>
								</>
							) : (
								<TableRow className="border-b border-gray-700/50 hover:bg-gray-700/30">
									<TableCell
										className="px-3 py-2 text-xs text-gray-400 italic text-right"
										colSpan={5}
									>
										Für den ausgewählten Zeitraum wurden keine Daten gefunden.
									</TableCell>
								</TableRow>
							)}
						</TableBody>
					</Table>
				</CardContent>
			</Card>

			<Card className="border-0 shadow-md bg-gray-800/40 overflow-hidden">
				<CardHeader className="p-3 border-b border-gray-700/50">
					<div className="flex items-center justify-between">
						<CardTitle className="text-sm font-medium flex items-center">
							<Users className="w-4 h-4 mr-1.5" /> Kunden
						</CardTitle>
						<Button
							variant="ghost"
							size="sm"
							onClick={() => setShowAllCustomers(!showAllCustomers)}
							className="p-1 h-6 w-6"
							title={
								showAllCustomers
									? "Nur aktive Kunden anzeigen"
									: "Alle Kunden anzeigen"
							}
						>
							{showAllCustomers ? (
								<Eye className="h-3 w-3" />
							) : (
								<EyeOff className="h-3 w-3" />
							)}
						</Button>
					</div>
				</CardHeader>
				<CardContent className="p-0">
					<Table>
						<TableHeader>
							<TableRow className="hover:bg-transparent border-b border-gray-700/50">
								<TableHead className="w-[40%] px-3 py-3 text-xs font-medium text-gray-400">
									Kunde
								</TableHead>
								<TableHead className="px-3 py-3 text-xs font-medium text-gray-400 text-right">
									Stunden
								</TableHead>
								<TableHead className="px-3 py-3 text-xs font-medium text-gray-400 text-right">
									Umsatz
								</TableHead>
								<TableHead className="px-3 py-3 text-xs font-medium text-gray-400 text-right">
									Leistungen
								</TableHead>
								<TableHead className="px-3 py-3 text-xs font-medium text-gray-400 text-right">
									Anfahrten
								</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{displayedCustomers.length > 0 ? (
								showAllCustomers ? (
									displayedCustomers.map((k) => (
										<TableRow
											key={k.kundeId}
											className="border-b border-gray-700/50 last:border-b-0 hover:bg-gray-700/30"
										>
											<TableCell className="px-3 py-2 text-xs font-medium">
												{k.kundeName}
											</TableCell>
											{k.stunden === 0 &&
											k.umsatz === 0 &&
											k.anzahlLeistungen === 0 &&
											k.anzahlAnfahrten === 0 ? (
												<TableCell
													className="px-3 py-2 text-xs text-gray-400 italic text-right"
													colSpan={4}
												>
													Für den ausgewählten Zeitraum wurden keine Daten
													gefunden.
												</TableCell>
											) : (
												<>
													<TableCell className="px-3 py-2 text-xs text-right">
														{formatHours(k.stunden)}
													</TableCell>
													<TableCell className="px-3 py-2 text-xs text-right">
														{formatCurrency(k.umsatz)}
													</TableCell>
													<TableCell className="px-3 py-2 text-xs text-right">
														{k.anzahlLeistungen}
													</TableCell>
													<TableCell className="px-3 py-2 text-xs text-right">
														{k.anzahlAnfahrten}
													</TableCell>
												</>
											)}
										</TableRow>
									))
								) : (
									displayedCustomers.map((k) => (
										<TableRow
											key={k.kundeId}
											className="border-b border-gray-700/50 last:border-b-0 hover:bg-gray-700/30"
										>
											<TableCell className="px-3 py-2 text-xs font-medium">
												{k.kundeName}
											</TableCell>
											<TableCell className="px-3 py-2 text-xs text-right">
												{formatHours(k.stunden)}
											</TableCell>
											<TableCell className="px-3 py-2 text-xs text-right">
												{formatCurrency(k.umsatz)}
											</TableCell>
											<TableCell className="px-3 py-2 text-xs text-right">
												{k.anzahlLeistungen}
											</TableCell>
											<TableCell className="px-3 py-2 text-xs text-right">
												{k.anzahlAnfahrten}
											</TableCell>
										</TableRow>
									))
								)
							) : (
								<TableRow className="border-b border-gray-700/50 hover:bg-gray-700/30">
									<TableCell
										className="px-3 py-2 text-xs text-gray-400 italic text-right"
										colSpan={5}
									>
										Für den ausgewählten Zeitraum wurden keine Daten gefunden.
									</TableCell>
								</TableRow>
							)}
						</TableBody>
					</Table>
				</CardContent>
			</Card>

			<Card className="border-0 shadow-md bg-gray-800/40 overflow-hidden">
				<CardHeader className="p-3 border-b border-gray-700/50">
					<div className="flex items-center justify-between">
						<CardTitle className="text-sm font-medium flex items-center">
							<Hourglass className="w-4 h-4 mr-1.5" /> Kontingente
						</CardTitle>
						<Button
							variant="ghost"
							size="sm"
							onClick={() =>
								setShowInactiveKontingente(!showInactiveKontingente)
							}
							className="p-1 h-6 w-6"
							title={
								showInactiveKontingente
									? "Inaktive/Verbrauchte ausblenden"
									: "Inaktive/Verbrauchte anzeigen"
							}
						>
							{showInactiveKontingente ? (
								<Eye className="h-3 w-3" />
							) : (
								<EyeOff className="h-3 w-3" />
							)}
						</Button>
					</div>
				</CardHeader>
				<CardContent className="p-0">
					<Table>
						<TableHeader>
							<TableRow className="hover:bg-transparent border-b border-gray-700/50">
								<TableHead className="w-[30%] px-3 py-3 text-xs font-medium text-gray-400">
									Kunde
								</TableHead>
								<TableHead className="px-3 py-3 text-xs font-medium text-gray-400 text-right">
									Verbraucht
								</TableHead>
								<TableHead className="px-3 py-3 text-xs font-medium text-gray-400 text-right">
									Rest
								</TableHead>
								<TableHead className="px-3 py-3 text-xs font-medium text-gray-400 text-right">
									Leistungen
								</TableHead>
								<TableHead className="px-3 py-3 text-xs font-medium text-gray-400 text-right">
									Restlaufzeit
								</TableHead>
								<TableHead className="px-3 py-3 text-xs font-medium text-gray-400 text-right">
									Auslastung
								</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{Object.keys(kontingentsByCustomer).length > 0 ? (
								Object.entries(kontingentsByCustomer).map(
									([customerName, kontingente]: [string, any]) => {
										const { aktiv, inaktiv } =
											getKontingentCounts(customerName);
										return (
											<React.Fragment key={`customer-${customerName}`}>
												<TableRow className="border-b border-gray-700/50 bg-gray-700/20">
													<TableCell className="px-3 py-2 text-xs font-medium">
														{customerName}
													</TableCell>
													<TableCell
														className="px-3 py-2 text-xs text-gray-400 italic text-right"
														colSpan={5}
													>
														({aktiv} aktiv - {inaktiv} inaktiv)
													</TableCell>
												</TableRow>
												{(kontingente as any[]).map((k: any) => {
													const usagePercentage = Math.min(
														100,
														Math.round(
															(k.verbrauchteStunden / k.stunden) * 100,
														),
													);
													const remainingDays = calculateRemainingDays(
														k.endDatum,
													);
													const anzahlLeistungen =
														getLeistungenCountForKontingent(k._id);

													return (
														<TableRow
															key={k._id}
															className={`border-b border-gray-700/50 last:border-b-0 hover:bg-gray-700/30 ${
																!k.istAktiv ? "opacity-60" : ""
															}`}
														>
															<TableCell className="px-6 py-2 text-xs">
																{k.name} {!k.istAktiv && "(Inaktiv)"}
															</TableCell>
															<TableCell className="px-3 py-2 text-xs text-right">
																{formatHours(k.verbrauchteStunden)}
															</TableCell>
															<TableCell className="px-3 py-2 text-xs text-right">
																{formatHours(k.restStunden)}
															</TableCell>
															<TableCell className="px-3 py-2 text-xs text-right">
																{anzahlLeistungen}
															</TableCell>
															<TableCell className="px-3 py-2 text-xs text-right">
																{renderRemainingDays(remainingDays)}
															</TableCell>
															<TableCell className="px-3 py-2 text-xs text-right">
																<div className="flex items-center justify-end gap-2">
																	<Progress
																		value={usagePercentage}
																		className="h-1.5 w-16"
																		indicatorClassName={
																			usagePercentage > 90
																				? "bg-red-500"
																				: usagePercentage > 75
																					? "bg-orange-500"
																					: "bg-blue-500"
																		}
																	/>
																	<span className="text-xs w-9 text-right">
																		{usagePercentage}%
																	</span>
																</div>
															</TableCell>
														</TableRow>
													);
												})}
											</React.Fragment>
										);
									},
								)
							) : (
								<TableRow className="border-b border-gray-700/50 hover:bg-gray-700/30">
									<TableCell
										className="px-3 py-2 text-xs text-gray-400 italic text-right"
										colSpan={6}
									>
										Für den ausgewählten Zeitraum wurden keine Daten gefunden.
									</TableCell>
								</TableRow>
							)}
						</TableBody>
					</Table>
				</CardContent>
			</Card>
		</div>
	);
}
