import { api } from "@/../convex/_generated/api";
import { PageLayout } from "@/components/layout/PageLayout";
import { calculateDateRange, toISODateString } from "@/lib/utils/dateUtils";
import { useQuery } from "convex/react";
import { useState } from "react";
import { Link } from "react-router-dom";

import { Button } from "@/components/_shared/Button";
import { BookOpen, FileText, Plus } from "lucide-react";
import { DashboardRecentActivity } from "./DashboardRecentActivity";
import { DashboardTables } from "./DashboardTables";
import {
	type DashboardFilter,
	useDashboardAnalytics,
} from "./hooks/useDashboardAnalytics";

export function DashboardPage() {
	const allLeistungen = useQuery(api.erstellung.leistung.list) || [];
	const allKontingente = useQuery(api.verwaltung.kontingente.list) || [];
	const allKunden = useQuery(api.verwaltung.kunden.list) || [];

	const isLoading =
		allLeistungen === undefined ||
		allKontingente === undefined ||
		allKunden === undefined;

	const [filter, setFilter] = useState<DashboardFilter>(() => {
		const { startDate, endDate } = calculateDateRange("month");
		return {
			zeitraum: "month",
			startDatum: toISODateString(startDate),
			endDatum: toISODateString(endDate),
			kundeId: "all",
			mitarbeiterId: "all",
		};
	});

	const dashboardDaten = useDashboardAnalytics({
		allLeistungen,
		allKontingente,
		filter,
	});

	return (
		<PageLayout
			title="Dashboard"
			subtitle="Übersicht Kennzahlen."
			action={
				<div className="flex items-center gap-3">
					<Link to="/erstellung/leistung">
						<Button size="sm" variant="outline" className="gap-1">
							<Plus className="h-4 w-4" />
							<span className="hidden sm:inline">Leistung</span>
						</Button>
					</Link>
					<Link to="/erstellung/lieferscheine">
						<Button size="sm" variant="outline" className="gap-1">
							<FileText className="h-4 w-4" />
							<span className="hidden sm:inline">Lieferschein</span>
						</Button>
					</Link>
					<Link to="/kunden/doku">
						<Button size="sm" variant="outline" className="gap-1">
							<BookOpen className="h-4 w-4" />
							<span className="hidden sm:inline">Doku</span>
						</Button>
					</Link>
				</div>
			}
		>
			<div className="space-y-4">
				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					<div className="md:col-span-2 space-y-4">
						<DashboardTables
							dashboardDaten={dashboardDaten}
							isLoading={isLoading}
							filter={filter}
							onFilterChange={setFilter}
							allKontingente={allKontingente}
							allKunden={allKunden}
							allLeistungen={allLeistungen}
						/>
					</div>
					<div>
						<DashboardRecentActivity filter={filter} isLoading={isLoading} />
					</div>
				</div>
			</div>
		</PageLayout>
	);
}

export default DashboardPage;
