import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { AngebotSettings } from "@/../convex/erstellung/angeboteConfig";
import { Button } from "@/components/_shared/Button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/_shared/Card";
import { AngebotPageForm } from "@/components/erstellung/angebote/AngebotPageForm";
import { CompactPositionForm } from "@/components/erstellung/angebote/CompactPositionForm";
import { CreateAngebotCorrectionDialog } from "@/components/erstellung/angebote/CreateAngebotCorrectionDialog";
import { FinalizeAngebotDialog } from "@/components/erstellung/angebote/FinalizeAngebotDialog";
import { UpdateStatusDialog } from "@/components/erstellung/angebote/UpdateStatusDialog";
import {
	<PERSON>runddatenCard,
	GrunddatenCardRef,
} from "@/components/erstellung/angebote/GrunddatenCard";
import { AngebotPDFDocument } from "@/components/erstellung/angebote/PDFDocument";
import { PageLayout } from "@/components/layout/PageLayout";
import { formatCurrency } from "@/lib/utils/formatUtils";
import { PDFDownloadLink, PDFViewer } from "@react-pdf/renderer";
import { useMutation, useQuery } from "convex/react";
import {
	AlertTriangle,
	ArrowLeft,
	Download,
	Eye,
	FileText,
	Plus,
	Save,
} from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Link, useLocation, useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

export function AngeboteDetailPage() {
	const { id } = useParams<{ id: string }>();
	const location = useLocation();
	const navigate = useNavigate();
	const [angebotId, setAngebotId] = useState<Id<"kunden_angebote"> | null>(
		null,
	);

	const [isFormVisible, setIsFormVisible] = useState(true);
	const [showPreview, setShowPreview] = useState(false);
	const [bemerkung, setBemerkung] = useState<string | undefined>();

	// Dialog states
	const [isCorrectionDialogOpen, setIsCorrectionDialogOpen] = useState(
		location.state?.openCorrectionDialog || false,
	);
	const [isFinalizationDialogOpen, setIsFinalizationDialogOpen] =
		useState(false);

	// Change tracking
	const [hasGrunddatenChanges, setHasGrunddatenChanges] = useState(false);
	const [hasPositionChanges, setHasPositionChanges] = useState(false);
	const [hasBemerkungChanges, setHasBemerkungChanges] = useState(false);

	// Refs for component access
	const grunddatenRef = useRef<GrunddatenCardRef>(null);

	// PDF settings
	const [includeHeaderInPDF, setIncludeHeaderInPDF] = useState(true);
	const [includeFooterInPDF, setIncludeFooterInPDF] = useState(true);
	const [showLogoInPDF, setShowLogoInPDF] = useState(true);
	const [includeLegalTextInPDF, setIncludeLegalTextInPDF] = useState(true);
	const [includeSignatureFieldInPDF, setIncludeSignatureFieldInPDF] =
		useState(true);
	const [processedLogoUrl, setProcessedLogoUrl] = useState<string | undefined>(
		undefined,
	);

	// Check if the id is a Convex ID or an angebot number
	// Convex IDs are typically 32 character alphanumeric strings
	// Angebot numbers follow the pattern "*********" or similar
	const isConvexId = id && id.length > 20 && !/^AG\d+/.test(id);
	const isUndefined = id === "undefined";

	const angebotDataById = useQuery(
		api.erstellung.angebot.get,
		isConvexId && !isUndefined ? { id: id as Id<"kunden_angebote"> } : "skip",
	);

	const angebotDataByNummer = useQuery(
		api.erstellung.angebot.getByNummer,
		!isConvexId ? { nummer: id || "" } : "skip",
	);

	const angebotData = isConvexId ? angebotDataById : angebotDataByNummer;

	const settings = useQuery(api.system.standards.getSettings, {
		type: "angebot",
	}) as AngebotSettings | null;

	const companySettings = useQuery(api.system.firmeneinstellungen.get);
	const customerData = angebotData
		? useQuery(api.verwaltung.kunden.get, { id: angebotData.kundenId })
		: undefined;

	const updateAngebot = useMutation(api.erstellung.angebot.update);
	const deleteAngebot = useMutation(api.erstellung.angebot.remove);

	// Update angebotId when data is loaded
	useEffect(() => {
		if (angebotData && !angebotId) {
			setAngebotId(angebotData._id);
		}
	}, [angebotData, angebotId]);

	// Update bemerkung when angebot data is loaded
	useEffect(() => {
		if (angebotData) {
			setBemerkung(angebotData.bemerkung);
		}
	}, [angebotData]);

	// Update bemerkung changes state
	useEffect(() => {
		setHasBemerkungChanges(bemerkung !== angebotData?.bemerkung);
	}, [bemerkung, angebotData?.bemerkung]);

	// Redirect to angebot nummer URL when finalized
	useEffect(() => {
		if (angebotData?.status === "fertig" && angebotData.nummer && isConvexId) {
			// If we're currently viewing via Convex ID but angebot is finalized with nummer, redirect
			navigate(`/erstellung/angebote/${angebotData.nummer}`, { replace: true });
		}
	}, [angebotData?.status, angebotData?.nummer, isConvexId, navigate]);

	// Load logo from settings
	useEffect(() => {
		if (settings?.logoPath) {
			try {
				setProcessedLogoUrl(settings.logoPath);
				const img = new Image();
				img.onerror = () => setProcessedLogoUrl(undefined);
				img.src = settings.logoPath;
			} catch (error) {
				setProcessedLogoUrl(undefined);
			}
		}
	}, [settings]);

	const handleUpdateBemerkung = useCallback(async () => {
		if (!angebotId) {
			toast.error("Angebot-ID nicht verfügbar.");
			return;
		}

		try {
			await updateAngebot({
				id: angebotId,
				bemerkung: bemerkung || undefined,
			});
			toast.success("Bemerkung erfolgreich aktualisiert.");
		} catch (error) {
			console.error("Fehler beim Aktualisieren der Bemerkung:", error);
			toast.error("Fehler beim Aktualisieren der Bemerkung.");
		}
	}, [angebotId, bemerkung, updateAngebot]);

	const handleFormSubmit = async (formData: any) => {
		if (!angebotId) return;
		try {
			// Include calculated totals from the form
			const updateData = {
				...formData,
				// Update totals with discount calculations if provided
				...(formData.gesamtsummeNetto && {
					gesamtsummeNetto: formData.gesamtsummeNetto,
				}),
				...(formData.gesamtsummeBrutto && {
					gesamtsummeBrutto: formData.gesamtsummeBrutto,
				}),
			};

			await updateAngebot({ id: angebotId, ...updateData });
			toast.success("Angebot erfolgreich aktualisiert");
		} catch (error: any) {
			toast.error(`Fehler beim Speichern: ${error.message}`);
		}
	};

	const handleGrunddatenUpdate = async (data: any) => {
		if (!angebotId) return;
		await updateAngebot({ id: angebotId, ...data });
	};

	// Save all pending changes
	const handleSaveAll = async () => {
		if (!angebotId) return;

		try {
			const promises = [];

			// Save bemerkung if changed
			if (hasBemerkungChanges) {
				promises.push(
					updateAngebot({
						id: angebotId,
						bemerkung: bemerkung || undefined,
					}),
				);
			}

			// Save grunddaten if changed
			if (hasGrunddatenChanges && grunddatenRef.current) {
				promises.push(grunddatenRef.current.save());
			}

			// Trigger form submit for positions if there are changes
			if (hasPositionChanges) {
				const form = document.getElementById(
					"position-form",
				) as HTMLFormElement;
				if (form) {
					form.dispatchEvent(
						new Event("submit", { cancelable: true, bubbles: true }),
					);
				}
			}

			// Wait for all saves to complete
			await Promise.all(promises);

			// Reset change states
			if (hasBemerkungChanges) {
				setHasBemerkungChanges(false);
			}

			toast.success("Alle Änderungen erfolgreich gespeichert");
		} catch (error) {
			toast.error("Fehler beim Speichern der Änderungen");
		}
	};

	// Check if there are any unsaved changes
	const hasAnyChanges =
		hasGrunddatenChanges || hasPositionChanges || hasBemerkungChanges;

	if (!angebotData) {
		return (
			<PageLayout
				title="Angebot wird geladen..."
				subtitle="Bitte warten Sie einen Moment"
				action={
					<Link to="/erstellung/angebote">
						<Button variant="outline">Zurück zur Übersicht</Button>
					</Link>
				}
			>
				<div className="flex justify-center items-center h-64">
					<div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500" />
				</div>
			</PageLayout>
		);
	}

	// Create PDF document instance
	const pdfKey = `pdf-${angebotData.nummer || angebotData._id}-${includeHeaderInPDF}-${includeFooterInPDF}-${showLogoInPDF}-${includeLegalTextInPDF}-${includeSignatureFieldInPDF}-${Date.now()}`;

	// Prepare company information for PDF
	const companyInfo = companySettings
		? {
				firmenname: companySettings.firmenname,
				strasse: companySettings.strasse,
				plz: companySettings.plz,
				ort: companySettings.ort,
				land: companySettings.land,
				telefon: companySettings.telefon,
				email: companySettings.email,
				website: companySettings.website,
				umsatzsteuerID: companySettings.umsatzsteuerID,
				handelsregisternummer: companySettings.handelsregisternummer,
				geschaeftsfuehrer: companySettings.geschaeftsfuehrer,
				zahlungsbedingungen: companySettings.zahlungsbedingungen,
				zahlungszielTage: companySettings.zahlungszielTage,
				standardUmsatzsteuer: companySettings.standardUmsatzsteuer,
			}
		: undefined;

	// Prepare customer information for PDF (use main location)
	const customerInfo = customerData
		? (() => {
				const mainLocation =
					customerData.standorte.find((s) => s.istHauptstandort) ||
					customerData.standorte[0];
				return mainLocation
					? {
							name: customerData.name,
							strasse: mainLocation.strasse,
							plz: mainLocation.plz,
							ort: mainLocation.ort,
							land: mainLocation.land,
						}
					: undefined;
			})()
		: undefined;

	const documentInstance = settings ? (
		<AngebotPDFDocument
			key={pdfKey}
			angebot={angebotData}
			includeHeader={includeHeaderInPDF}
			includeFooter={includeFooterInPDF}
			showLogo={showLogoInPDF}
			includeLegalText={includeLegalTextInPDF}
			includeSignatureField={includeSignatureFieldInPDF}
			logoUrl={processedLogoUrl}
			firmenName={companySettings?.firmenname || "innov8-IT"}
			firmenFusszeileText={settings.fusszeileText}
			legalText={settings.legalText}
			signatureText={settings.signatureText}
			formatCurrency={formatCurrency}
			companyInfo={companyInfo}
			customerInfo={customerInfo}
		/>
	) : null;

	// PDF filename logic: drafts = AG<ID>.pdf, finalized = AG00001.pdf
	const pdfFileName =
		angebotData.status === "entwurf"
			? `AG${angebotData._id}.pdf`
			: `${angebotData.nummer}.pdf`;

	return (
		<PageLayout
			title={`Angebot: ${angebotData.nummer || "Entwurf"}`}
			subtitle={`Kunde: ${angebotData.kundeName} | Erstellt am: ${angebotData.erstelltAmFormatiert}`}
			action={
				<div className="flex items-center gap-2">
					<Link to="/erstellung/angebote">
						<Button variant="outline" className="gap-2">
							<ArrowLeft className="h-4 w-4" />
							Zurück zur Übersicht
						</Button>
					</Link>
				</div>
			}
		>
			<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
				{/* Left Column: Controls and Options */}
				<div className="lg:col-span-1 space-y-6">
					{/* Status Card */}
					<Card className="shadow-lg border-0">
						<CardHeader className="p-4">
							<CardTitle className="text-base font-medium">Status</CardTitle>
						</CardHeader>
						<CardContent className="p-4 pt-0">
							<div className="space-y-4">
								{angebotData.status === "entwurf" ? (
									<div className="flex items-center text-blue-400">
										<FileText className="h-5 w-5 mr-2" />
										<span>
											{angebotData.istKorrektur
												? "Korrektur-Entwurf"
												: "Angebot-Entwurf"}
										</span>
									</div>
								) : angebotData.istKorrektur ? (
									<div className="flex items-center text-orange-400">
										<AlertTriangle className="h-5 w-5 mr-2" />
										<span>Dies ist eine Korrektur</span>
									</div>
								) : angebotData.hatKorrektur ? (
									<div className="flex items-center text-orange-400">
										<AlertTriangle className="h-5 w-5 mr-2" />
										<span>Zu diesem Angebot existieren Korrekturen</span>
									</div>
								) : (
									<div className="flex items-center text-green-400">
										<FileText className="h-5 w-5 mr-2" />
										<span>Aktives Angebot</span>
									</div>
								)}

								{/* Bemerkung */}
								<div className="mt-4">
									<h3 className="text-sm font-medium mb-1">Bemerkung:</h3>
									{angebotData.status === "entwurf" ? (
										<textarea
											className="w-full p-2 border rounded-md text-sm bg-gray-800 text-white"
											value={bemerkung || ""}
											onChange={(e) => setBemerkung(e.target.value)}
											placeholder="Bemerkung hinzufügen (optional)"
											rows={3}
										/>
									) : angebotData.bemerkung ? (
										<p className="text-sm text-gray-400">
											{angebotData.bemerkung}
										</p>
									) : (
										<p className="text-sm text-gray-400 italic">
											Keine Bemerkung vorhanden
										</p>
									)}
								</div>

								{/* Action Buttons */}
								<div className="flex flex-wrap gap-2 mt-4">
									{/* Download Button */}
									{documentInstance && (
										<PDFDownloadLink
											key={`download-${pdfKey}`}
											document={documentInstance}
											fileName={pdfFileName}
										>
											{({ loading }) => (
												<Button
													disabled={loading}
													variant="outline"
													className="h-10 w-10 p-0"
													title="PDF herunterladen"
												>
													<Download className="h-5 w-5" />
												</Button>
											)}
										</PDFDownloadLink>
									)}

									{/* Vorschau Toggle Button - always show */}
									<Button
										variant="outline"
										onClick={() => setShowPreview(!showPreview)}
										className="h-10 w-10 p-0"
										title={
											showPreview ? "Vorschau ausblenden" : "Vorschau anzeigen"
										}
									>
										<Eye className="h-5 w-5" />
									</Button>

									{/* Finalisieren Button - nur für Entwürfe */}
									{angebotData.status === "entwurf" && (
										<Button
											onClick={() => setIsFinalizationDialogOpen(true)}
											className="h-10 w-10 p-0 bg-green-600 hover:bg-green-700"
											title={
												angebotData.istKorrektur
													? "Korrektur finalisieren"
													: "Angebot finalisieren"
											}
										>
											<FileText className="h-5 w-5" />
										</Button>
									)}

									{/* Korrektur Button - für alle finalisierten Angebote */}
									{angebotData.status === "fertig" && (
										<Button
											variant="outline"
											onClick={() => setIsCorrectionDialogOpen(true)}
											className="h-10 w-10 p-0"
											title="Korrektur erstellen"
										>
											<AlertTriangle className="h-5 w-5" />
										</Button>
									)}

									{/* Spacer and Unified Save Button */}
									<div className="flex-1" />
									{angebotData.status === "entwurf" && hasAnyChanges && (
										<Button
											onClick={handleSaveAll}
											className="h-10 w-10 p-0 bg-green-600 hover:bg-green-700"
											title="Änderungen speichern"
										>
											<Save className="h-5 w-5" />
										</Button>
									)}
								</div>
							</div>
						</CardContent>
					</Card>

					{/* Grunddaten Card */}
					<GrunddatenCard
						ref={grunddatenRef}
						angebot={{
							kundenId: angebotData.kundenId,
							kundeName: angebotData.kundeName,
							gueltigBis: angebotData.gueltigBis,
							status: angebotData.status,
						}}
						onUpdate={handleGrunddatenUpdate}
						onChangesDetected={setHasGrunddatenChanges}
					/>

					{/* PDF Settings Card */}
					<Card className="shadow-lg border-0">
						<CardHeader className="p-4">
							<CardTitle className="text-base font-medium">
								PDF-Einstellungen
							</CardTitle>
						</CardHeader>
						<CardContent className="p-4 pt-0">
							<div className="space-y-3">
								<div className="flex items-center justify-between">
									<label htmlFor="includeHeader" className="text-sm">
										Header anzeigen
									</label>
									<input
										type="checkbox"
										id="includeHeader"
										checked={includeHeaderInPDF}
										onChange={(e) => setIncludeHeaderInPDF(e.target.checked)}
										className="h-4 w-4"
									/>
								</div>
								<div className="flex items-center justify-between">
									<label
										htmlFor="showLogo"
										className={`text-sm ${!processedLogoUrl ? "opacity-50" : ""}`}
									>
										Logo anzeigen
										{!processedLogoUrl &&
											" (Logo wird aus der Konfiguration geladen)"}
									</label>
									<input
										type="checkbox"
										id="showLogo"
										checked={showLogoInPDF}
										onChange={(e) => setShowLogoInPDF(e.target.checked)}
										disabled={!processedLogoUrl}
										className="h-4 w-4"
									/>
								</div>
								<div className="flex items-center justify-between">
									<label htmlFor="includeFooter" className="text-sm">
										Footer anzeigen
									</label>
									<input
										type="checkbox"
										id="includeFooter"
										checked={includeFooterInPDF}
										onChange={(e) => setIncludeFooterInPDF(e.target.checked)}
										className="h-4 w-4"
									/>
								</div>
								<div className="flex items-center justify-between">
									<label htmlFor="includeLegalText" className="text-sm">
										Rechtlichen Hinweis anzeigen
									</label>
									<input
										type="checkbox"
										id="includeLegalText"
										checked={includeLegalTextInPDF}
										onChange={(e) => setIncludeLegalTextInPDF(e.target.checked)}
										className="h-4 w-4"
									/>
								</div>
								<div className="flex items-center justify-between">
									<label htmlFor="includeSignatureField" className="text-sm">
										Unterschriftsfeld anzeigen
									</label>
									<input
										type="checkbox"
										id="includeSignatureField"
										checked={includeSignatureFieldInPDF}
										onChange={(e) =>
											setIncludeSignatureFieldInPDF(e.target.checked)
										}
										className="h-4 w-4"
									/>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>

				{/* Right Column: PDF Preview or Form */}
				<div className="lg:col-span-2">
					{showPreview ? (
						<Card className="shadow-lg border-0 h-[calc(100vh-12rem)]">
							{typeof window !== "undefined" && documentInstance && (
								<PDFViewer
									key={pdfKey}
									width="100%"
									height="100%"
									className="rounded-md"
									showToolbar={true}
								>
									{documentInstance}
								</PDFViewer>
							)}
						</Card>
					) : (
						<div className="space-y-6">
							{/* Header */}
							<div className="flex items-center justify-between">
								<div>
									<h2 className="text-lg font-medium text-white">
										Angebot bearbeiten
									</h2>
									<p className="text-sm text-gray-400">
										Ändern Sie die Positionen des Angebots
									</p>
								</div>
							</div>

							{angebotData.status === "entwurf" ? (
								<CompactPositionForm
									initialData={{ positionen: angebotData.positionen }}
									onSubmit={handleFormSubmit}
									isSubmitting={false}
									formId="position-form"
									onChangesDetected={setHasPositionChanges}
								/>
							) : (
								<Card className="shadow-lg border-0">
									<CardHeader className="p-4 pb-2">
										<CardTitle className="text-base font-medium">
											Angebotspositionen
										</CardTitle>
									</CardHeader>
									<CardContent className="p-4 pt-0">
										<div className="space-y-4">
											{angebotData.positionen.map((position, index) => (
												<div
													key={position.id}
													className="bg-gray-800/50 p-4 rounded-lg border border-gray-700"
												>
													<div className="grid md:grid-cols-5 gap-3 text-sm">
														<div>
															<span className="text-gray-400 text-xs uppercase tracking-wide">
																Position {index + 1}
															</span>
															<p className="font-medium text-white">
																{position.titel}
															</p>
															{position.beschreibung && (
																<p className="text-gray-400 text-xs mt-1">
																	{position.beschreibung}
																</p>
															)}
														</div>
														<div>
															<span className="text-gray-400 text-xs uppercase tracking-wide">
																Menge
															</span>
															<p className="text-white">{position.menge}</p>
														</div>
														<div>
															<span className="text-gray-400 text-xs uppercase tracking-wide">
																Einheit
															</span>
															<p className="text-white">{position.einheit}</p>
														</div>
														<div>
															<span className="text-gray-400 text-xs uppercase tracking-wide">
																Einzelpreis
															</span>
															<p className="text-white">
																{formatCurrency(position.einzelpreis)}
															</p>
														</div>
														<div>
															<span className="text-gray-400 text-xs uppercase tracking-wide">
																Gesamt
															</span>
															<p className="text-white font-medium">
																{formatCurrency(
																	position.menge * position.einzelpreis,
																)}
															</p>
														</div>
													</div>
												</div>
											))}

											<div className="bg-gray-800/30 p-4 rounded-lg border border-gray-600">
												<div className="space-y-2 text-sm">
													<div className="flex justify-between">
														<span>Zwischensumme (Netto)</span>
														<span>
															{formatCurrency(angebotData.gesamtsummeNetto)}
														</span>
													</div>
													<div className="flex justify-between">
														<span>Umsatzsteuer (19%)</span>
														<span>
															{formatCurrency(
																angebotData.gesamtsummeBrutto -
																	angebotData.gesamtsummeNetto,
															)}
														</span>
													</div>
													<div className="flex justify-between font-bold text-base border-t border-gray-600 pt-2">
														<span>Gesamtsumme (Brutto)</span>
														<span>
															{formatCurrency(angebotData.gesamtsummeBrutto)}
														</span>
													</div>
												</div>
											</div>
										</div>
									</CardContent>
								</Card>
							)}
						</div>
					)}
				</div>
			</div>

			{/* Dialogs */}
			{isCorrectionDialogOpen && angebotId && (
				<CreateAngebotCorrectionDialog
					isOpen={isCorrectionDialogOpen}
					onClose={() => setIsCorrectionDialogOpen(false)}
					originalId={angebotId}
				/>
			)}

			{isFinalizationDialogOpen && angebotId && (
				<FinalizeAngebotDialog
					isOpen={isFinalizationDialogOpen}
					onClose={() => setIsFinalizationDialogOpen(false)}
					angebotId={angebotId}
					isCorrection={angebotData.istKorrektur}
				/>
			)}
		</PageLayout>
	);
}

export default AngeboteDetailPage;
