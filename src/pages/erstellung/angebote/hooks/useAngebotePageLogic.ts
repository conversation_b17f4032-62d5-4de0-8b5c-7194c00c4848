import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { calculateDateRange, toISODateString } from "@/lib/utils/dateUtils";
import { useMutation, useQuery } from "convex/react";
import { useEffect, useMemo, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { toast } from "sonner";

export interface AngebotFilter {
	status: "all" | "entwurf" | "fertig";
	zeitraum: "all" | "last7" | "last30" | "month" | "lastMonth" | "custom";
	startDatum: string;
	endDatum: string;
	kundeId: string;
}

export function useAngebotePageLogic() {
	const [searchParams, setSearchParams] = useSearchParams();
	const [isNewAngebotDialogOpen, setIsNewAngebotDialogOpen] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");
	const [filter, setFilter] = useState<AngebotFilter>({
		status: "all",
		zeitraum: "month", // Standardmäßig "Dieser Monat" anzeigen
		startDatum: "",
		endDatum: "",
		kundeId: "all", // Standardmäßig alle Kunden anzeigen
	});
	const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

	// Dialog automatisch öffnen, wenn openDialog=true in URL
	useEffect(() => {
		if (searchParams.get("openDialog") === "true") {
			setIsNewAngebotDialogOpen(true);
			// URL-Parameter entfernen
			const newParams = new URLSearchParams(searchParams);
			newParams.delete("openDialog");
			setSearchParams(newParams, { replace: true });
		}
	}, [searchParams, setSearchParams]);

	// Mutation zum Löschen eines Angebots
	const removeAngebot = useMutation(api.erstellung.angebot.remove);

	// Setze die Start- und Enddaten basierend auf dem Zeitraum
	useMemo(() => {
		if (filter.zeitraum !== "all" && filter.zeitraum !== "custom") {
			const { startDate, endDate } = calculateDateRange(filter.zeitraum);
			endDate.setHours(23, 59, 59, 999);

			setFilter((prev) => ({
				...prev,
				startDatum: toISODateString(startDate),
				endDatum: toISODateString(endDate),
			}));
		}
	}, [filter.zeitraum]);

	const angebotGruppen = useQuery(api.erstellung.angebot.list) || [];

	// Filtere die Angebote basierend auf den Filterkriterien
	const filteredAngebotGruppen = useMemo(() => {
		return angebotGruppen.filter((gruppe) => {
			const angebot = gruppe.hauptAngebot;

			// Suche
			const searchMatch =
				searchTerm === "" ||
				(angebot.nummer &&
					angebot.nummer.toLowerCase().includes(searchTerm.toLowerCase())) ||
				angebot.kundeName.toLowerCase().includes(searchTerm.toLowerCase());

			// Status-Filter
			const statusMatch =
				filter.status === "all" || angebot.status === filter.status;

			// Zeitraum-Filter
			let dateMatch = true;
			if (filter.zeitraum !== "all") {
				const angebotTime = angebot.erstelltAm;
				const startTime = filter.startDatum
					? new Date(filter.startDatum).getTime()
					: 0;
				const endTime = filter.endDatum
					? new Date(filter.endDatum).getTime() + 86400000 - 1 // Ende des Tages
					: new Date(8640000000000000).getTime(); // Maximales Datum

				dateMatch = angebotTime >= startTime && angebotTime <= endTime;
			}

			// Kunden-Filter
			const kundeMatch =
				filter.kundeId === "all" || angebot.kundenId === filter.kundeId;

			return searchMatch && statusMatch && dateMatch && kundeMatch;
		});
	}, [angebotGruppen, searchTerm, filter]);

	// Handler für Filteränderungen
	const handleFilterChange = (key: keyof AngebotFilter, value: string) => {
		setFilter((prev) => ({
			...prev,
			[key]: value,
			// Wenn der Zeitraum geändert wird und nicht "custom" ist, werden Start- und Enddatum automatisch gesetzt
			...(key === "zeitraum" && value !== "custom" && value !== prev.zeitraum
				? { startDatum: "", endDatum: "" }
				: {}),
		}));
	};

	// Handler zum Zurücksetzen der Filter
	const resetFilters = () => {
		setSearchTerm("");
		setFilter({
			status: "all",
			zeitraum: "month",
			startDatum: "",
			endDatum: "",
			kundeId: "all",
		});
	};

	// Handler zum Löschen eines Angebots
	const handleDelete = async (id: Id<"kunden_angebote">) => {
		if (
			window.confirm(
				"Möchten Sie dieses Angebot wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.",
			)
		) {
			try {
				await removeAngebot({ korrekturId: id });
				toast.success("Angebot erfolgreich gelöscht");
			} catch (error: any) {
				toast.error(
					`Fehler beim Löschen: ${error.message || "Unbekannter Fehler"}`,
				);
			}
		}
	};

	// Handler zum Ein-/Ausklappen einer Zeile
	const toggleExpandRow = (id: string) => {
		setExpandedRows((prevExpandedRows) => {
			const newExpandedRows = new Set(prevExpandedRows);
			if (newExpandedRows.has(id)) {
				newExpandedRows.delete(id);
			} else {
				newExpandedRows.add(id);
			}
			return newExpandedRows;
		});
	};

	// Handler zum Schließen des Dialogs
	const handleCloseNewAngebotDialog = () => {
		setIsNewAngebotDialogOpen(false);
		// URL-Parameter entfernen wenn vorhanden
		const newParams = new URLSearchParams(searchParams);
		if (newParams.has("openDialog")) {
			newParams.delete("openDialog");
			setSearchParams(newParams, { replace: true });
		}
	};

	return {
		isNewAngebotDialogOpen,
		setIsNewAngebotDialogOpen,
		handleCloseNewAngebotDialog,
		searchTerm,
		setSearchTerm,
		filter,
		expandedRows,
		filteredAngebotGruppen,
		handleFilterChange,
		resetFilters,
		handleDelete,
		toggleExpandRow,
	};
}
