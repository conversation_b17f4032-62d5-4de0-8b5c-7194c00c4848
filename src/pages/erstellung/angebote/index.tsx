import { Button } from "@/components/_shared/Button";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import { AngebotFilterControls } from "@/components/erstellung/angebote/AngebotFilterControls";
import { AngebotRow } from "@/components/erstellung/angebote/AngebotRow";
import { AngeboteEmptyState } from "@/components/erstellung/angebote/AngeboteEmptyState";
import { NewAngebotDialog } from "@/components/erstellung/angebote/NewAngebotDialog";
import { PageLayout } from "@/components/layout/PageLayout";
import { StandardDataTable } from "@/components/layout/StandardDataTable";
import { FileText, Plus } from "lucide-react";
import {
	AngebotFilter,
	useAngebotePageLogic,
} from "./hooks/useAngebotePageLogic";

export function AngebotePage() {
	const {
		isNewAngebotDialogOpen,
		setIsNewAngebotDialogOpen,
		handleCloseNewAngebotDialog,
		searchTerm,
		setSearchTerm,
		filter,
		expandedRows,
		filteredAngebotGruppen,
		handleFilterChange,
		resetFilters,
		handleDelete,
		toggleExpandRow,
	} = useAngebotePageLogic();

	const actionButton = (
		<Button
			onClick={() => setIsNewAngebotDialogOpen(true)}
			size="sm"
			className="gap-1"
		>
			<Plus className="h-4 w-4" />
			Neues Angebot
		</Button>
	);

	return (
		<PageLayout
			title="Angebote"
			subtitle="Erstellen und verwalten Sie Ihre Angebote"
			action={actionButton}
		>
			<StandardDataTable
				title="Angebotsübersicht"
				infoSlot={
					<>
						<FileText className="h-3.5 w-3.5 opacity-70" />
						<span>
							{filteredAngebotGruppen.length}{" "}
							{filteredAngebotGruppen.length === 1 ? "Angebot" : "Angebote"}
						</span>
					</>
				}
				filterSlot={
					<AngebotFilterControls
						filter={filter}
						searchTerm={searchTerm}
						onFilterChange={handleFilterChange}
						onSearchTermChange={setSearchTerm}
					/>
				}
			>
				<div className="overflow-x-auto">
					<Table>
						<TableHeader>
							<TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
								<TableHead className="font-medium">Nummer</TableHead>
								<TableHead className="font-medium">Kunde</TableHead>
								<TableHead className="font-medium">Erstellt am</TableHead>
								<TableHead className="font-medium">Status</TableHead>
								<TableHead className="font-medium text-right">
									Gesamtsumme
								</TableHead>
								<TableHead className="w-24 text-center">Aktionen</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{filteredAngebotGruppen.length === 0 ? (
								<TableRow>
									<TableCell
										colSpan={6}
										className="text-center py-8 text-gray-400"
									>
										<AngeboteEmptyState
											searchTerm={searchTerm}
											filterStatus={filter.status}
											filterZeitraum={filter.zeitraum}
											filterKundeId={filter.kundeId}
											onResetFilters={resetFilters}
										/>
									</TableCell>
								</TableRow>
							) : (
								filteredAngebotGruppen.map((gruppe) => (
									<AngebotRow
										key={gruppe.hauptAngebot._id}
										angebot={gruppe.hauptAngebot}
										korrekturen={gruppe.korrekturen}
										original={gruppe.original}
										onDelete={handleDelete}
										isExpanded={expandedRows.has(
											gruppe.hauptAngebot._id.toString(),
										)}
										onToggleExpand={() =>
											toggleExpandRow(gruppe.hauptAngebot._id.toString())
										}
									/>
								))
							)}
						</TableBody>
					</Table>
				</div>
			</StandardDataTable>

			<NewAngebotDialog
				isOpen={isNewAngebotDialogOpen}
				onClose={handleCloseNewAngebotDialog}
			/>
		</PageLayout>
	);
}

export default AngebotePage;
