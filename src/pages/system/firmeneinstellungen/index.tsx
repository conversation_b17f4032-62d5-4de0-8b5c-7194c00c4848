import { api } from "@/../convex/_generated/api";
import { Button } from "@/components/_shared/Button";
import { <PERSON>, CardContent, CardHeader } from "@/components/_shared/Card";
import { Input } from "@/components/_shared/Input";
import { Label } from "@/components/_shared/Label";
import { Textarea } from "@/components/_shared/Textarea";
import { PageLayout } from "@/components/layout/PageLayout";
import { useMutation, useQuery } from "convex/react";
import { Building, Check, Save } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface FirmeneinstellungenFormData {
	firmenname: string;
	rechtsform: string;
	strasse: string;
	plz: string;
	ort: string;
	land: string;
	telefon: string;
	fax: string;
	email: string;
	website: string;
	steuernummer: string;
	umsatzsteuerID: string;
	handelsregisternummer: string;
	amtsgericht: string;
	geschaeftsfuehrer: string;
	bankname: string;
	iban: string;
	bic: string;
	zahlungsbedingungen: string;
	lieferbedingungen: string;
	zahlungszielTage: number;
	standardUmsatzsteuer: number;
	waehrung: string;
}

export function FirmeneinstellungenPage() {
	const [formData, setFormData] = useState<FirmeneinstellungenFormData>({
		firmenname: "",
		rechtsform: "",
		strasse: "",
		plz: "",
		ort: "",
		land: "Deutschland",
		telefon: "",
		fax: "",
		email: "",
		website: "",
		steuernummer: "",
		umsatzsteuerID: "",
		handelsregisternummer: "",
		amtsgericht: "",
		geschaeftsfuehrer: "",
		bankname: "",
		iban: "",
		bic: "",
		zahlungsbedingungen:
			"Zahlbar innerhalb von 14 Tagen nach Rechnungsdatum ohne Abzug.",
		lieferbedingungen:
			"Lieferung erfolgt nach den Allgemeinen Geschäftsbedingungen.",
		zahlungszielTage: 14,
		standardUmsatzsteuer: 19,
		waehrung: "EUR",
	});

	const [isLoading, setIsLoading] = useState(false);

	// Queries and mutations
	const existingSettings = useQuery(api.system.firmeneinstellungen.get);
	const defaultSettings = useQuery(api.system.firmeneinstellungen.getDefaults);
	const createOrUpdateSettings = useMutation(
		api.system.firmeneinstellungen.createOrUpdate,
	);

	// Load existing settings or defaults
	useEffect(() => {
		if (existingSettings) {
			setFormData({
				firmenname: existingSettings.firmenname || "",
				rechtsform: existingSettings.rechtsform || "",
				strasse: existingSettings.strasse || "",
				plz: existingSettings.plz || "",
				ort: existingSettings.ort || "",
				land: existingSettings.land || "Deutschland",
				telefon: existingSettings.telefon || "",
				fax: existingSettings.fax || "",
				email: existingSettings.email || "",
				website: existingSettings.website || "",
				steuernummer: existingSettings.steuernummer || "",
				umsatzsteuerID: existingSettings.umsatzsteuerID || "",
				handelsregisternummer: existingSettings.handelsregisternummer || "",
				amtsgericht: existingSettings.amtsgericht || "",
				geschaeftsfuehrer: existingSettings.geschaeftsfuehrer || "",
				bankname: existingSettings.bankname || "",
				iban: existingSettings.iban || "",
				bic: existingSettings.bic || "",
				zahlungsbedingungen: existingSettings.zahlungsbedingungen || "",
				lieferbedingungen: existingSettings.lieferbedingungen || "",
				zahlungszielTage: existingSettings.zahlungszielTage || 14,
				standardUmsatzsteuer: existingSettings.standardUmsatzsteuer || 19,
				waehrung: existingSettings.waehrung || "EUR",
			});
		} else if (defaultSettings && !existingSettings) {
			setFormData(defaultSettings);
		}
	}, [existingSettings, defaultSettings]);

	const handleInputChange = (
		field: keyof FirmeneinstellungenFormData,
		value: string | number,
	) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async () => {
		setIsLoading(true);
		try {
			await createOrUpdateSettings(formData);
			toast.success("Firmeneinstellungen erfolgreich gespeichert");
		} catch (error) {
			toast.error(
				`Fehler beim Speichern: ${error instanceof Error ? error.message : "Unbekannter Fehler"}`,
			);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<PageLayout
			title="Firmeneinstellungen"
			subtitle="Verwalten Sie die grundlegenden Informationen Ihres Unternehmens"
			action={
				<Button onClick={handleSubmit} disabled={isLoading} className="gap-2">
					<Save className="h-4 w-4" />
					{isLoading ? "Speichern..." : "Speichern"}
				</Button>
			}
		>
			<div className="space-y-6">
				{/* Basic Company Information */}
				<Card>
					<CardHeader>
						<div className="flex items-center gap-2">
							<Building className="h-5 w-5 text-blue-400" />
							<h3 className="text-lg font-semibold">Grunddaten</h3>
						</div>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<Label htmlFor="firmenname">Firmenname *</Label>
								<Input
									id="firmenname"
									value={formData.firmenname}
									onChange={(e) =>
										handleInputChange("firmenname", e.target.value)
									}
									placeholder="innov8-IT"
									required
								/>
							</div>
							<div>
								<Label htmlFor="rechtsform">Rechtsform</Label>
								<Input
									id="rechtsform"
									value={formData.rechtsform}
									onChange={(e) =>
										handleInputChange("rechtsform", e.target.value)
									}
									placeholder="GmbH"
								/>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Address Information */}
				<Card>
					<CardHeader>
						<h3 className="text-lg font-semibold">Adresse</h3>
					</CardHeader>
					<CardContent className="space-y-4">
						<div>
							<Label htmlFor="strasse">Straße *</Label>
							<Input
								id="strasse"
								value={formData.strasse}
								onChange={(e) => handleInputChange("strasse", e.target.value)}
								placeholder="Musterstraße 123"
								required
							/>
						</div>
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							<div>
								<Label htmlFor="plz">PLZ *</Label>
								<Input
									id="plz"
									value={formData.plz}
									onChange={(e) => handleInputChange("plz", e.target.value)}
									placeholder="12345"
									required
								/>
							</div>
							<div>
								<Label htmlFor="ort">Ort *</Label>
								<Input
									id="ort"
									value={formData.ort}
									onChange={(e) => handleInputChange("ort", e.target.value)}
									placeholder="Musterstadt"
									required
								/>
							</div>
							<div>
								<Label htmlFor="land">Land</Label>
								<Input
									id="land"
									value={formData.land}
									onChange={(e) => handleInputChange("land", e.target.value)}
									placeholder="Deutschland"
								/>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Contact Information */}
				<Card>
					<CardHeader>
						<h3 className="text-lg font-semibold">Kontaktdaten</h3>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<Label htmlFor="telefon">Telefon</Label>
								<Input
									id="telefon"
									value={formData.telefon}
									onChange={(e) => handleInputChange("telefon", e.target.value)}
									placeholder="+49 123 456789"
								/>
							</div>
							<div>
								<Label htmlFor="fax">Fax</Label>
								<Input
									id="fax"
									value={formData.fax}
									onChange={(e) => handleInputChange("fax", e.target.value)}
									placeholder="+49 123 456790"
								/>
							</div>
							<div>
								<Label htmlFor="email">E-Mail</Label>
								<Input
									id="email"
									type="email"
									value={formData.email}
									onChange={(e) => handleInputChange("email", e.target.value)}
									placeholder="<EMAIL>"
								/>
							</div>
							<div>
								<Label htmlFor="website">Website</Label>
								<Input
									id="website"
									value={formData.website}
									onChange={(e) => handleInputChange("website", e.target.value)}
									placeholder="https://www.innov8-it.de"
								/>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Legal and Tax Information */}
				<Card>
					<CardHeader>
						<h3 className="text-lg font-semibold">
							Rechtliche und steuerliche Angaben
						</h3>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<Label htmlFor="steuernummer">Steuernummer</Label>
								<Input
									id="steuernummer"
									value={formData.steuernummer}
									onChange={(e) =>
										handleInputChange("steuernummer", e.target.value)
									}
									placeholder="123/456/78901"
								/>
							</div>
							<div>
								<Label htmlFor="umsatzsteuerID">Umsatzsteuer-ID</Label>
								<Input
									id="umsatzsteuerID"
									value={formData.umsatzsteuerID}
									onChange={(e) =>
										handleInputChange("umsatzsteuerID", e.target.value)
									}
									placeholder="DE123456789"
								/>
							</div>
							<div>
								<Label htmlFor="handelsregisternummer">
									Handelsregisternummer
								</Label>
								<Input
									id="handelsregisternummer"
									value={formData.handelsregisternummer}
									onChange={(e) =>
										handleInputChange("handelsregisternummer", e.target.value)
									}
									placeholder="HRB 12345"
								/>
							</div>
							<div>
								<Label htmlFor="amtsgericht">Amtsgericht</Label>
								<Input
									id="amtsgericht"
									value={formData.amtsgericht}
									onChange={(e) =>
										handleInputChange("amtsgericht", e.target.value)
									}
									placeholder="Amtsgericht Musterstadt"
								/>
							</div>
						</div>
						<div>
							<Label htmlFor="geschaeftsfuehrer">Geschäftsführer</Label>
							<Input
								id="geschaeftsfuehrer"
								value={formData.geschaeftsfuehrer}
								onChange={(e) =>
									handleInputChange("geschaeftsfuehrer", e.target.value)
								}
								placeholder="Max Mustermann"
							/>
						</div>
					</CardContent>
				</Card>

				{/* Banking Information */}
				<Card>
					<CardHeader>
						<h3 className="text-lg font-semibold">Bankverbindung</h3>
					</CardHeader>
					<CardContent className="space-y-4">
						<div>
							<Label htmlFor="bankname">Bankname</Label>
							<Input
								id="bankname"
								value={formData.bankname}
								onChange={(e) => handleInputChange("bankname", e.target.value)}
								placeholder="Musterbank AG"
							/>
						</div>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<Label htmlFor="iban">IBAN</Label>
								<Input
									id="iban"
									value={formData.iban}
									onChange={(e) => handleInputChange("iban", e.target.value)}
									placeholder="DE89 3704 0044 0532 0130 00"
								/>
							</div>
							<div>
								<Label htmlFor="bic">BIC</Label>
								<Input
									id="bic"
									value={formData.bic}
									onChange={(e) => handleInputChange("bic", e.target.value)}
									placeholder="COBADEFFXXX"
								/>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Payment and Delivery Terms */}
				<Card>
					<CardHeader>
						<h3 className="text-lg font-semibold">
							Zahlungs- und Lieferbedingungen
						</h3>
					</CardHeader>
					<CardContent className="space-y-4">
						<div>
							<Label htmlFor="zahlungsbedingungen">Zahlungsbedingungen</Label>
							<Textarea
								id="zahlungsbedingungen"
								value={formData.zahlungsbedingungen}
								onChange={(e) =>
									handleInputChange("zahlungsbedingungen", e.target.value)
								}
								placeholder="Zahlbar innerhalb von 14 Tagen nach Rechnungsdatum ohne Abzug."
								rows={3}
							/>
						</div>
						<div>
							<Label htmlFor="lieferbedingungen">Lieferbedingungen</Label>
							<Textarea
								id="lieferbedingungen"
								value={formData.lieferbedingungen}
								onChange={(e) =>
									handleInputChange("lieferbedingungen", e.target.value)
								}
								placeholder="Lieferung erfolgt nach den Allgemeinen Geschäftsbedingungen."
								rows={3}
							/>
						</div>
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							<div>
								<Label htmlFor="zahlungszielTage">Zahlungsziel (Tage)</Label>
								<Input
									id="zahlungszielTage"
									type="number"
									min="0"
									max="365"
									value={formData.zahlungszielTage}
									onChange={(e) =>
										handleInputChange(
											"zahlungszielTage",
											parseInt(e.target.value) || 0,
										)
									}
								/>
							</div>
							<div>
								<Label htmlFor="standardUmsatzsteuer">
									Standard-Umsatzsteuer (%)
								</Label>
								<Input
									id="standardUmsatzsteuer"
									type="number"
									min="0"
									max="100"
									step="0.01"
									value={formData.standardUmsatzsteuer}
									onChange={(e) =>
										handleInputChange(
											"standardUmsatzsteuer",
											parseFloat(e.target.value) || 0,
										)
									}
								/>
							</div>
							<div>
								<Label htmlFor="waehrung">Währung</Label>
								<Input
									id="waehrung"
									value={formData.waehrung}
									onChange={(e) =>
										handleInputChange("waehrung", e.target.value)
									}
									placeholder="EUR"
								/>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>
		</PageLayout>
	);
}
