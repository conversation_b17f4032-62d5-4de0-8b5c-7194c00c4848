import { defaultAngebotConfig } from "@/../convex/erstellung/angeboteConfig";
import { defaultLieferscheinConfig } from "@/../convex/erstellung/lieferscheineConfig";
import { defaultUebersichtConfig } from "@/../convex/erstellung/uebersichtenConfig";
import { Card, CardContent, CardHeader } from "@/components/_shared/Card";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>onte<PERSON>,
	Ta<PERSON>List,
	TabsTrigger,
} from "@/components/_shared/Tabs";
import { PageLayout } from "@/components/layout/PageLayout";
import {
	Check,
	ChevronDown,
	ChevronUp,
	FileCheck,
	FileText,
	Mail,
	Settings,
	X,
	DollarSign,
} from "lucide-react";
import { useEffect, useState } from "react";

type StandardsTab = "angebote" | "uebersicht" | "lieferschein" | "email";

export function StandardsPage() {
	// Get settings from the new config files
	const angebotSettings = defaultAngebotConfig.settings;
	const angebotEmailSettings = defaultAngebotConfig.emailSettings;
	const angebotEmailTemplate = defaultAngebotConfig.emailTemplate;
	const uebersichtSettings = defaultUebersichtConfig.settings;
	const lieferscheinSettings = defaultLieferscheinConfig.settings;
	const uebersichtEmailSettings = defaultUebersichtConfig.emailSettings;
	const lieferscheinEmailSettings = defaultLieferscheinConfig.emailSettings;

	// Get email templates from the new config files
	const uebersichtEmailTemplate = defaultUebersichtConfig.emailTemplate;
	const lieferscheinEmailTemplate = defaultLieferscheinConfig.emailTemplate;

	// State for selected tab
	const [selectedTab, setSelectedTab] = useState<StandardsTab>("angebote");

	// State for logo images
	const [angebotLogo, setAngebotLogo] = useState<string | null>(null);
	const [uebersichtLogo, setUebersichtLogo] = useState<string | null>(null);
	const [lieferscheinLogo, setLieferscheinLogo] = useState<string | null>(null);

	// State for expandable text fields
	const [isLegalTextExpanded, setIsLegalTextExpanded] = useState(false);
	const [expandedEmailFields, setExpandedEmailFields] = useState<
		Record<string, boolean>
	>({});

	// Load logo images
	useEffect(() => {
		// Load logo paths from configuration
		try {
			setAngebotLogo(angebotSettings.logoPath);
		} catch (error) {
			setAngebotLogo(null);
		}

		try {
			setUebersichtLogo(uebersichtSettings.logoPath);
		} catch (error) {
			setUebersichtLogo(null);
		}

		try {
			setLieferscheinLogo(lieferscheinSettings.logoPath);
		} catch (error) {
			setLieferscheinLogo(null);
		}
	}, []);

	// Helper function to toggle email field expansion
	const toggleEmailFieldExpansion = (fieldName: string) => {
		setExpandedEmailFields((prev) => ({
			...prev,
			[fieldName]: !prev[fieldName],
		}));
	};

	// Helper function to render boolean values with icons
	const renderBoolean = (value: boolean) => (
		<span className={value ? "text-green-400" : "text-red-400"}>
			{value ? (
				<Check className="inline-block h-4 w-4 mr-1" />
			) : (
				<X className="inline-block h-4 w-4 mr-1" />
			)}
			{value ? "Ja" : "Nein"}
		</span>
	);

	// Setting item component
	const SettingItem = ({
		label,
		value,
		isExpandable = false,
		isExpanded = false,
		onToggleExpand = () => {},
	}: {
		label: string;
		value: React.ReactNode;
		isExpandable?: boolean;
		isExpanded?: boolean;
		onToggleExpand?: () => void;
	}) => {
		const isTextLong = typeof value === "string" && value.length > 100;
		const shouldBeExpandable = isExpandable || isTextLong;

		return (
			<div className="py-1.5 border-b border-gray-800/40">
				<div className="flex justify-between items-center">
					<span className="text-sm font-medium text-blue-400 w-1/3">
						{label}
					</span>
					<div className="flex flex-1 justify-between items-center">
						<div
							className={`text-sm ${shouldBeExpandable && !isExpanded ? "line-clamp-1" : ""}`}
						>
							{value}
						</div>
						{shouldBeExpandable && (
							<button
								onClick={onToggleExpand}
								className="text-gray-400 hover:text-gray-300 ml-2 flex-shrink-0"
								aria-label={isExpanded ? "Collapse" : "Expand"}
							>
								{isExpanded ? (
									<ChevronUp className="h-4 w-4" />
								) : (
									<ChevronDown className="h-4 w-4" />
								)}
							</button>
						)}
					</div>
				</div>
			</div>
		);
	};

	return (
		<PageLayout
			title="Systemeinstellungen"
			subtitle="Übersicht der Standardeinstellungen für PDF-Dokumente"
		>
			<Card className="shadow-lg border-0 overflow-hidden">
				<Tabs
					value={selectedTab}
					onValueChange={(value) => setSelectedTab(value as StandardsTab)}
					className="w-full"
				>
					<CardHeader className="border-b border-gray-700/50 p-4">
						<div className="flex flex-col sm:flex-row justify-between sm:items-center gap-3">
							<TabsList>
								<TabsTrigger value="angebote">
									<DollarSign className="h-4 w-4 mr-1.5" /> Angebote
								</TabsTrigger>
								<TabsTrigger value="uebersicht">
									<FileText className="h-4 w-4 mr-1.5" /> Übersichten
								</TabsTrigger>
								<TabsTrigger value="lieferschein">
									<FileCheck className="h-4 w-4 mr-1.5" /> Lieferscheine
								</TabsTrigger>
								<TabsTrigger value="email">
									<Mail className="h-4 w-4 mr-1.5" /> E-Mail
								</TabsTrigger>
							</TabsList>
							<div className="text-xs text-gray-400 flex items-center gap-1.5">
								<Settings className="h-3 w-3" />
								Dokumenteinstellungen
							</div>
						</div>
					</CardHeader>

					{/* Angebote Tab Content */}
					<TabsContent value="angebote" className="m-0">
						<CardContent className="p-6">
							<div className="flex flex-col">
								{/* Logo at the top */}
								{angebotLogo && (
									<div className="mb-6 flex justify-center">
										<div className="relative max-w-full max-h-28">
											<img
												src={angebotLogo}
												alt="Angebot Logo"
												className="max-h-28 object-contain"
												onError={(e) => {
													e.currentTarget.src =
														"https://via.placeholder.com/200x100?text=Logo+nicht+gefunden";
												}}
											/>
											{!angebotSettings.showLogo && (
												<div className="absolute inset-0 bg-black/60 flex items-center justify-center">
													<span className="text-white text-xs px-2 py-0.5 bg-red-500/80 rounded">
														Deaktiviert
													</span>
												</div>
											)}
										</div>
									</div>
								)}

								{/* E-Mail Einstellungen für Angebote */}
								<div className="border-b border-gray-700/50 pb-4 mb-4">
									<h3 className="text-lg font-semibold text-blue-400 mb-3 flex items-center gap-2">
										<Mail className="h-5 w-5" />
										E-Mail Einstellungen
									</h3>
									<div className="border border-gray-700/30 rounded-lg p-4 space-y-1.5">
										<SettingItem
											label="defaultSendToMitarbeiter"
											value={renderBoolean(
												angebotEmailSettings.defaultSendToMitarbeiter,
											)}
										/>
										<SettingItem
											label="defaultSendToKunde"
											value={renderBoolean(
												angebotEmailSettings.defaultSendToKunde,
											)}
										/>
									</div>
								</div>

								{/* E-Mail Template für Angebote */}
								<div className="border-b border-gray-700/50 pb-4 mb-4">
									<h3 className="text-lg font-semibold text-blue-400 mb-3 flex items-center gap-2">
										<Mail className="h-5 w-5" />
										E-Mail Template
									</h3>
									<div className="border border-gray-700/30 rounded-lg p-4 space-y-1.5">
										<SettingItem
											label="subject"
											value={angebotEmailTemplate.subject}
										/>
										<SettingItem
											label="body"
											value={angebotEmailTemplate.body}
											isExpandable={true}
											isExpanded={expandedEmailFields["angebot-body"] || false}
											onToggleExpand={() =>
												toggleEmailFieldExpansion("angebot-body")
											}
										/>
									</div>
								</div>

								{/* Settings in vertical layout, one per row */}
								<div className="flex flex-col space-y-1.5">
									{/* General Settings */}
									<SettingItem
										label="logoPath"
										value={angebotSettings.logoPath}
									/>

									<SettingItem
										label="fusszeileText"
										value={angebotSettings.fusszeileText}
									/>

									<SettingItem
										label="signatureText"
										value={angebotSettings.signatureText}
									/>

									<SettingItem
										label="includeHeader"
										value={renderBoolean(angebotSettings.includeHeader)}
									/>

									<SettingItem
										label="showLogo"
										value={renderBoolean(angebotSettings.showLogo)}
									/>

									<SettingItem
										label="includeFooter"
										value={renderBoolean(angebotSettings.includeFooter)}
									/>

									<SettingItem
										label="includeSignatureField"
										value={renderBoolean(angebotSettings.includeSignatureField)}
									/>

									{/* Legal text with expand/collapse functionality */}
									<SettingItem
										label="legalText"
										value={angebotSettings.legalText}
										isExpandable={true}
										isExpanded={isLegalTextExpanded}
										onToggleExpand={() =>
											setIsLegalTextExpanded(!isLegalTextExpanded)
										}
									/>
								</div>
							</div>
						</CardContent>
					</TabsContent>

					{/* Übersichten Tab Content */}
					<TabsContent value="uebersicht" className="m-0">
						<CardContent className="p-6">
							<div className="flex flex-col">
								{/* Logo at the top */}
								{uebersichtLogo && (
									<div className="mb-6 flex justify-center">
										<div className="relative max-w-full max-h-28">
											<img
												src={uebersichtLogo}
												alt="Übersicht Logo"
												className="max-h-28 object-contain"
												onError={(e) => {
													e.currentTarget.src =
														"https://via.placeholder.com/200x100?text=Logo+nicht+gefunden";
												}}
											/>
											{!uebersichtSettings.showLogo && (
												<div className="absolute inset-0 bg-black/60 flex items-center justify-center">
													<span className="text-white text-xs px-2 py-0.5 bg-red-500/80 rounded">
														Deaktiviert
													</span>
												</div>
											)}
										</div>
									</div>
								)}

								{/* E-Mail Einstellungen für Übersichten */}
								<div className="border-b border-gray-700/50 pb-4 mb-4">
									<h3 className="text-lg font-semibold text-blue-400 mb-3 flex items-center gap-2">
										<Mail className="h-5 w-5" />
										E-Mail Einstellungen
									</h3>
									<div className="border border-gray-700/30 rounded-lg p-4 space-y-1.5">
										<SettingItem
											label="defaultSendToMitarbeiter"
											value={renderBoolean(
												uebersichtEmailSettings.defaultSendToMitarbeiter,
											)}
										/>
										<SettingItem
											label="defaultSendToKunde"
											value={renderBoolean(
												uebersichtEmailSettings.defaultSendToKunde,
											)}
										/>
									</div>
								</div>

								{/* E-Mail Template für Übersichten */}
								<div className="border-b border-gray-700/50 pb-4 mb-4">
									<h3 className="text-lg font-semibold text-blue-400 mb-3 flex items-center gap-2">
										<Mail className="h-5 w-5" />
										E-Mail Template
									</h3>
									<div className="border border-gray-700/30 rounded-lg p-4 space-y-1.5">
										<SettingItem
											label="subject"
											value={uebersichtEmailTemplate.subject}
										/>
										<SettingItem
											label="body"
											value={uebersichtEmailTemplate.body}
											isExpandable={true}
											isExpanded={
												expandedEmailFields["uebersicht-body"] || false
											}
											onToggleExpand={() =>
												toggleEmailFieldExpansion("uebersicht-body")
											}
										/>
									</div>
								</div>

								{/* Settings in vertical layout, one per row */}
								<div className="flex flex-col space-y-1.5">
									{/* General Settings */}
									<SettingItem
										label="logoPath"
										value={uebersichtSettings.logoPath}
									/>

									<SettingItem
										label="fusszeileText"
										value={uebersichtSettings.fusszeileText}
									/>

									<SettingItem
										label="includeHeader"
										value={renderBoolean(uebersichtSettings.includeHeader)}
									/>

									<SettingItem
										label="showLogo"
										value={renderBoolean(uebersichtSettings.showLogo)}
									/>

									<SettingItem
										label="includeFooter"
										value={renderBoolean(uebersichtSettings.includeFooter)}
									/>

									<SettingItem
										label="includeLeistungsuebersicht"
										value={renderBoolean(
											uebersichtSettings.includeLeistungsuebersicht,
										)}
									/>

									<SettingItem
										label="includeKontingentuebersicht"
										value={renderBoolean(
											uebersichtSettings.includeKontingentuebersicht,
										)}
									/>

									<SettingItem
										label="includeSummary"
										value={renderBoolean(uebersichtSettings.includeSummary)}
									/>
								</div>
							</div>
						</CardContent>
					</TabsContent>

					{/* Lieferscheine Tab Content */}
					<TabsContent value="lieferschein" className="m-0">
						<CardContent className="p-6">
							<div className="flex flex-col">
								{/* Logo at the top */}
								{lieferscheinLogo && (
									<div className="mb-6 flex justify-center">
										<div className="relative max-w-full max-h-28">
											<img
												src={lieferscheinLogo}
												alt="Lieferschein Logo"
												className="max-h-28 object-contain"
												onError={(e) => {
													e.currentTarget.src =
														"https://via.placeholder.com/200x100?text=Logo+nicht+gefunden";
												}}
											/>
											{!lieferscheinSettings.showLogo && (
												<div className="absolute inset-0 bg-black/60 flex items-center justify-center">
													<span className="text-white text-xs px-2 py-0.5 bg-red-500/80 rounded">
														Deaktiviert
													</span>
												</div>
											)}
										</div>
									</div>
								)}

								{/* E-Mail Einstellungen für Lieferscheine */}
								<div className="border-b border-gray-700/50 pb-4 mb-4">
									<h3 className="text-lg font-semibold text-blue-400 mb-3 flex items-center gap-2">
										<Mail className="h-5 w-5" />
										E-Mail Einstellungen
									</h3>
									<div className="border border-gray-700/30 rounded-lg p-4 space-y-1.5">
										<SettingItem
											label="defaultSendToMitarbeiter"
											value={renderBoolean(
												lieferscheinEmailSettings.defaultSendToMitarbeiter,
											)}
										/>
										<SettingItem
											label="defaultSendToKunde"
											value={renderBoolean(
												lieferscheinEmailSettings.defaultSendToKunde,
											)}
										/>
									</div>
								</div>

								{/* E-Mail Template für Lieferscheine */}
								<div className="border-b border-gray-700/50 pb-4 mb-4">
									<h3 className="text-lg font-semibold text-blue-400 mb-3 flex items-center gap-2">
										<Mail className="h-5 w-5" />
										E-Mail Template
									</h3>
									<div className="border border-gray-700/30 rounded-lg p-4 space-y-1.5">
										<SettingItem
											label="subject"
											value={lieferscheinEmailTemplate.subject}
										/>
										<SettingItem
											label="body"
											value={lieferscheinEmailTemplate.body}
											isExpandable={true}
											isExpanded={
												expandedEmailFields["lieferschein-body"] || false
											}
											onToggleExpand={() =>
												toggleEmailFieldExpansion("lieferschein-body")
											}
										/>
									</div>
								</div>

								{/* Settings in vertical layout, one per row */}
								<div className="flex flex-col space-y-1.5">
									{/* General Settings */}
									<SettingItem
										label="logoPath"
										value={lieferscheinSettings.logoPath}
									/>

									<SettingItem
										label="fusszeileText"
										value={lieferscheinSettings.fusszeileText}
									/>

									<SettingItem
										label="signatureText"
										value={lieferscheinSettings.signatureText}
									/>

									<SettingItem
										label="includeHeader"
										value={renderBoolean(lieferscheinSettings.includeHeader)}
									/>

									<SettingItem
										label="showLogo"
										value={renderBoolean(lieferscheinSettings.showLogo)}
									/>

									<SettingItem
										label="includeFooter"
										value={renderBoolean(lieferscheinSettings.includeFooter)}
									/>

									<SettingItem
										label="includeSignatureField"
										value={renderBoolean(
											lieferscheinSettings.includeSignatureField,
										)}
									/>

									{/* Legal text with expand/collapse functionality */}
									<SettingItem
										label="legalText"
										value={lieferscheinSettings.legalText}
										isExpandable={true}
										isExpanded={isLegalTextExpanded}
										onToggleExpand={() =>
											setIsLegalTextExpanded(!isLegalTextExpanded)
										}
									/>
								</div>
							</div>
						</CardContent>
					</TabsContent>

					{/* Email Tab Content */}
					<TabsContent value="email" className="m-0">
						<CardContent className="p-6">
							<div className="flex flex-col space-y-4">
								{/* Email Configuration Section */}
								<div className="border-b border-gray-700/50 pb-4">
									<h3 className="text-lg font-semibold text-blue-400 mb-3 flex items-center gap-2">
										<Settings className="h-5 w-5" />
										TurboSMTP Konfiguration
									</h3>
									<div className="flex flex-col space-y-1.5">
										<SettingItem
											label="TURBOSMTP_CONSUMER_KEY"
											value="Wird über Convex Environment Variables geladen"
										/>

										<SettingItem
											label="TURBOSMTP_CONSUMER_SECRET"
											value="Wird über Convex Environment Variables geladen"
										/>

										<SettingItem
											label="EMAIL_FROM"
											value="Wird über Convex Environment Variables geladen"
										/>

										<SettingItem
											label="EMAIL_FROM_NAME"
											value="Wird über Convex Environment Variables geladen"
										/>

										<SettingItem
											label="TURBOSMTP_API_URL"
											value="Wird über Convex Environment Variables geladen"
										/>
									</div>
								</div>
							</div>
						</CardContent>
					</TabsContent>
				</Tabs>
			</Card>
		</PageLayout>
	);
}

export default StandardsPage;
